"""
数据模型定义
"""
from datetime import datetime
from dataclasses import dataclass
from typing import Optional


@dataclass
class Todo:
    """任务数据模型"""
    id: Optional[int] = None
    title: str = ""
    description: str = ""
    priority: int = 1  # 1=低, 2=中, 3=高
    category_id: Optional[int] = None
    folder_id: Optional[int] = None
    due_date: Optional[datetime] = None
    completed: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    @property
    def priority_text(self) -> str:
        """获取优先级文本"""
        priority_map = {1: "低", 2: "中", 3: "高"}
        return priority_map.get(self.priority, "低")
    
    @property
    def priority_color(self) -> str:
        """获取优先级颜色"""
        color_map = {1: "#95a5a6", 2: "#f39c12", 3: "#e74c3c"}
        return color_map.get(self.priority, "#95a5a6")
    
    @property
    def is_overdue(self) -> bool:
        """检查是否过期"""
        if self.due_date and not self.completed:
            return datetime.now() > self.due_date
        return False


@dataclass
class Category:
    """分类数据模型"""
    id: Optional[int] = None
    name: str = ""
    color: str = "#3498db"
    icon: str = ""
    created_at: Optional[datetime] = None


@dataclass
class Folder:
    """文件夹数据模型"""
    id: Optional[int] = None
    name: str = ""
    parent_id: Optional[int] = None
    color: str = "#95a5a6"
    created_at: Optional[datetime] = None


@dataclass
class Setting:
    """设置数据模型"""
    key: str = ""
    value: str = ""
    updated_at: Optional[datetime] = None
