"""
主题管理器
"""
import wx
from typing import Dict, Any


class ThemeManager:
    """主题管理类"""
    
    def __init__(self):
        """初始化主题管理器"""
        self.current_theme = 'light'
        self.themes = self._load_themes()
    
    def _load_themes(self) -> Dict[str, Dict[str, Any]]:
        """加载主题配置"""
        return {
            'light': {
                'name': '明亮主题',
                'colors': {
                    # 主色调
                    'primary': '#3498db',
                    'secondary': '#2ecc71',
                    'accent': '#e74c3c',
                    'warning': '#f39c12',
                    
                    # 背景色
                    'bg_primary': '#ffffff',
                    'bg_secondary': '#f8f9fa',
                    'bg_dark': '#2c3e50',
                    'bg_card': '#ffffff',
                    'bg_hover': '#ecf0f1',
                    
                    # 文字色
                    'text_primary': '#2c3e50',
                    'text_secondary': '#7f8c8d',
                    'text_light': '#bdc3c7',
                    'text_white': '#ffffff',
                    
                    # 边框色
                    'border_light': '#e9ecef',
                    'border_medium': '#dee2e6',
                    'border_dark': '#adb5bd',
                    
                    # 优先级颜色
                    'priority_low': '#95a5a6',
                    'priority_medium': '#f39c12',
                    'priority_high': '#e74c3c',
                    
                    # 状态颜色
                    'success': '#2ecc71',
                    'error': '#e74c3c',
                    'info': '#3498db',
                }
            },
            'dark': {
                'name': '暗黑主题',
                'colors': {
                    # 主色调
                    'primary': '#3498db',
                    'secondary': '#2ecc71',
                    'accent': '#e74c3c',
                    'warning': '#f39c12',
                    
                    # 背景色
                    'bg_primary': '#2c3e50',
                    'bg_secondary': '#34495e',
                    'bg_dark': '#1a252f',
                    'bg_card': '#34495e',
                    'bg_hover': '#3d566e',
                    
                    # 文字色
                    'text_primary': '#ecf0f1',
                    'text_secondary': '#bdc3c7',
                    'text_light': '#95a5a6',
                    'text_white': '#ffffff',
                    
                    # 边框色
                    'border_light': '#4a6741',
                    'border_medium': '#5d6d7e',
                    'border_dark': '#7f8c8d',
                    
                    # 优先级颜色
                    'priority_low': '#95a5a6',
                    'priority_medium': '#f39c12',
                    'priority_high': '#e74c3c',
                    
                    # 状态颜色
                    'success': '#2ecc71',
                    'error': '#e74c3c',
                    'info': '#3498db',
                }
            }
        }
    
    def get_theme_names(self) -> list:
        """获取所有主题名称"""
        return [theme_data['name'] for theme_data in self.themes.values()]
    
    def get_theme_keys(self) -> list:
        """获取所有主题键"""
        return list(self.themes.keys())
    
    def set_theme(self, theme_key: str) -> bool:
        """设置当前主题"""
        if theme_key in self.themes:
            self.current_theme = theme_key
            return True
        return False
    
    def get_current_theme(self) -> str:
        """获取当前主题键"""
        return self.current_theme
    
    def get_color(self, color_name: str, theme_key: str = None) -> wx.Colour:
        """获取颜色"""
        if theme_key is None:
            theme_key = self.current_theme
        
        if theme_key not in self.themes:
            theme_key = 'light'
        
        color_hex = self.themes[theme_key]['colors'].get(color_name, '#000000')
        return wx.Colour(color_hex)
    
    def get_theme_colors(self, theme_key: str = None) -> Dict[str, str]:
        """获取主题的所有颜色"""
        if theme_key is None:
            theme_key = self.current_theme
        
        if theme_key not in self.themes:
            theme_key = 'light'
        
        return self.themes[theme_key]['colors'].copy()
    
    def is_dark_theme(self, theme_key: str = None) -> bool:
        """判断是否为暗色主题"""
        if theme_key is None:
            theme_key = self.current_theme
        
        return theme_key == 'dark'
    
    def apply_theme_to_window(self, window: wx.Window, theme_key: str = None) -> None:
        """将主题应用到窗口"""
        if theme_key is None:
            theme_key = self.current_theme

        # 设置当前主题
        self.current_theme = theme_key

        # 设置窗口背景色
        bg_color = self.get_color('bg_primary', theme_key)
        window.SetBackgroundColour(bg_color)

        # 递归应用到子控件
        self._apply_theme_to_children(window, theme_key)

        # 刷新显示
        window.Refresh()
        window.Update()

    def _apply_theme_to_children(self, parent: wx.Window, theme_key: str) -> None:
        """递归应用主题到子控件"""
        for child in parent.GetChildren():
            try:
                if isinstance(child, wx.Panel):
                    # 区分不同类型的面板
                    if hasattr(child, 'GetName') and 'sidebar' in child.GetName().lower():
                        child.SetBackgroundColour(self.get_color('bg_secondary', theme_key))
                    else:
                        child.SetBackgroundColour(self.get_color('bg_primary', theme_key))
                elif isinstance(child, wx.StaticText):
                    child.SetForegroundColour(self.get_color('text_primary', theme_key))
                elif isinstance(child, wx.Button):
                    child.SetBackgroundColour(self.get_color('bg_card', theme_key))
                    child.SetForegroundColour(self.get_color('text_primary', theme_key))
                elif isinstance(child, wx.TextCtrl):
                    child.SetBackgroundColour(self.get_color('bg_card', theme_key))
                    child.SetForegroundColour(self.get_color('text_primary', theme_key))
                elif isinstance(child, wx.ListCtrl):
                    child.SetBackgroundColour(self.get_color('bg_card', theme_key))
                    child.SetForegroundColour(self.get_color('text_primary', theme_key))
                elif isinstance(child, wx.TreeCtrl):
                    child.SetBackgroundColour(self.get_color('bg_card', theme_key))
                    child.SetForegroundColour(self.get_color('text_primary', theme_key))

                # 刷新子控件
                child.Refresh()

                # 递归处理子控件
                if hasattr(child, 'GetChildren'):
                    self._apply_theme_to_children(child, theme_key)
            except Exception as e:
                # 忽略无法设置主题的控件
                print(f"应用主题到控件失败: {e}")
                continue
