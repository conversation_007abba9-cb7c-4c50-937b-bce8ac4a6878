"""
添加任务对话框
"""
import wx
import wx.adv
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from ui.styles import UIStyles
from core.models import Todo


class AddTodoDialog(wx.Dialog):
    """添加任务对话框"""
    
    def __init__(self, parent, categories=None, folders=None, theme='light'):
        """初始化对话框"""
        super().__init__(parent, title="新建任务", size=(850, 580),
                        style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER)

        # 设置最小尺寸以适应横向布局，确保所有内容都能完整显示
        self.SetMinSize((800, 530))
        
        self.categories = categories or []
        self.folders = folders or []
        self.theme = theme
        self.todo_data = {}
        
        # 初始化UI
        self.init_ui()
        self.setup_events()
        
        # 居中显示
        self.Center()
    
    def init_ui(self) -> None:
        """初始化用户界面"""
        # 创建主面板
        main_panel = wx.Panel(self)
        main_panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.theme))
        
        # 创建主布局
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题区域
        title_label = wx.StaticText(main_panel, label="创建新任务")
        title_label.SetFont(UIStyles.get_font('large', bold=True))
        title_label.SetForegroundColour(UIStyles.get_color('text_primary', self.theme))
        main_sizer.Add(title_label, 0, wx.ALL | wx.CENTER, UIStyles.SPACING['lg'])
        
        # 表单区域
        form_panel = self.create_form_panel(main_panel)
        main_sizer.Add(form_panel, 1, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        # 按钮区域
        button_panel = self.create_button_panel(main_panel)
        main_sizer.Add(button_panel, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        main_panel.SetSizer(main_sizer)
        
        # 设置对话框布局
        dialog_sizer = wx.BoxSizer(wx.VERTICAL)
        dialog_sizer.Add(main_panel, 1, wx.EXPAND)
        self.SetSizer(dialog_sizer)
    
    def create_form_panel(self, parent) -> wx.Panel:
        """创建表单面板 - 横向布局"""
        form_panel = wx.Panel(parent)
        form_panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.theme))

        # 使用网格布局管理器，减少垂直间距以适应更多内容
        form_sizer = wx.FlexGridSizer(rows=0, cols=1, vgap=UIStyles.SPACING['xs'], hgap=0)
        form_sizer.AddGrowableCol(0)

        # 第一行：任务标题（全宽）
        title_panel = wx.Panel(form_panel)
        title_sizer = wx.BoxSizer(wx.VERTICAL)

        title_label = wx.StaticText(title_panel, label="任务标题 *")
        title_label.SetFont(UIStyles.get_font('medium', bold=True))
        title_sizer.Add(title_label, 0, wx.ALL, UIStyles.SPACING['xs'])

        self.title_ctrl = wx.TextCtrl(title_panel, size=(-1, 35))
        self.title_ctrl.SetFont(UIStyles.get_font('medium'))
        title_sizer.Add(self.title_ctrl, 0, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, UIStyles.SPACING['xs'])

        title_panel.SetSizer(title_sizer)
        form_sizer.Add(title_panel, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['xs'])

        # 第二行：任务描述（全宽）
        desc_panel = wx.Panel(form_panel)
        desc_sizer = wx.BoxSizer(wx.VERTICAL)

        desc_label = wx.StaticText(desc_panel, label="任务描述")
        desc_label.SetFont(UIStyles.get_font('medium', bold=True))
        desc_sizer.Add(desc_label, 0, wx.ALL, UIStyles.SPACING['xs'])

        self.desc_ctrl = wx.TextCtrl(desc_panel, style=wx.TE_MULTILINE, size=(-1, 80))
        self.desc_ctrl.SetFont(UIStyles.get_font('medium'))
        desc_sizer.Add(self.desc_ctrl, 0, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, UIStyles.SPACING['xs'])

        desc_panel.SetSizer(desc_sizer)
        form_sizer.Add(desc_panel, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['xs'])

        # 第三行：优先级、分类、文件夹（三列横向布局）
        options_panel = wx.Panel(form_panel)
        options_sizer = wx.FlexGridSizer(rows=2, cols=3, vgap=UIStyles.SPACING['xs'], hgap=UIStyles.SPACING['md'])
        options_sizer.AddGrowableCol(0, 1)
        options_sizer.AddGrowableCol(1, 1)
        options_sizer.AddGrowableCol(2, 1)

        # 优先级选择
        priority_label = wx.StaticText(options_panel, label="优先级")
        priority_label.SetFont(UIStyles.get_font('medium', bold=True))
        options_sizer.Add(priority_label, 0, wx.ALIGN_LEFT | wx.ALIGN_CENTER_VERTICAL)

        # 分类选择
        category_label = wx.StaticText(options_panel, label="分类")
        category_label.SetFont(UIStyles.get_font('medium', bold=True))
        options_sizer.Add(category_label, 0, wx.ALIGN_LEFT | wx.ALIGN_CENTER_VERTICAL)

        # 文件夹选择
        folder_label = wx.StaticText(options_panel, label="文件夹")
        folder_label.SetFont(UIStyles.get_font('medium', bold=True))
        options_sizer.Add(folder_label, 0, wx.ALIGN_LEFT | wx.ALIGN_CENTER_VERTICAL)

        # 控件行
        priority_choices = ["低", "中", "高"]
        self.priority_choice = wx.Choice(options_panel, choices=priority_choices)
        self.priority_choice.SetSelection(0)  # 默认选择"低"
        options_sizer.Add(self.priority_choice, 0, wx.EXPAND)

        category_choices = ["无"] + [cat.name for cat in self.categories]
        self.category_choice = wx.Choice(options_panel, choices=category_choices)
        self.category_choice.SetSelection(0)  # 默认选择"无"
        options_sizer.Add(self.category_choice, 0, wx.EXPAND)

        folder_choices = ["无"] + [folder.name for folder in self.folders]
        self.folder_choice = wx.Choice(options_panel, choices=folder_choices)
        self.folder_choice.SetSelection(0)  # 默认选择"无"
        options_sizer.Add(self.folder_choice, 0, wx.EXPAND)

        options_panel.SetSizer(options_sizer)
        form_sizer.Add(options_panel, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['xs'])

        # 第四行：到期时间设置
        due_panel = wx.Panel(form_panel)
        due_main_sizer = wx.BoxSizer(wx.VERTICAL)

        # 到期时间标题和复选框
        due_header_sizer = wx.BoxSizer(wx.HORIZONTAL)
        due_label = wx.StaticText(due_panel, label="到期时间")
        due_label.SetFont(UIStyles.get_font('medium', bold=True))
        due_header_sizer.Add(due_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, UIStyles.SPACING['md'])

        self.due_checkbox = wx.CheckBox(due_panel, label="设置到期时间")
        due_header_sizer.Add(self.due_checkbox, 0, wx.ALIGN_CENTER_VERTICAL)

        due_main_sizer.Add(due_header_sizer, 0, wx.ALL, UIStyles.SPACING['xs'])

        # 日期时间选择器（横向排列）
        datetime_panel = wx.Panel(due_panel)
        datetime_sizer = wx.FlexGridSizer(rows=2, cols=2, vgap=UIStyles.SPACING['xs'], hgap=UIStyles.SPACING['md'])
        datetime_sizer.AddGrowableCol(0, 1)
        datetime_sizer.AddGrowableCol(1, 1)

        # 标签行
        date_label = wx.StaticText(datetime_panel, label="日期:")
        date_label.SetFont(UIStyles.get_font('small'))
        datetime_sizer.Add(date_label, 0, wx.ALIGN_LEFT | wx.ALIGN_CENTER_VERTICAL)

        time_label = wx.StaticText(datetime_panel, label="时间:")
        time_label.SetFont(UIStyles.get_font('small'))
        datetime_sizer.Add(time_label, 0, wx.ALIGN_LEFT | wx.ALIGN_CENTER_VERTICAL)

        # 控件行
        self.date_picker = wx.adv.DatePickerCtrl(datetime_panel)
        self.date_picker.SetValue(wx.DateTime.Today())
        self.date_picker.Enable(False)
        datetime_sizer.Add(self.date_picker, 0, wx.EXPAND)

        self.time_picker = wx.adv.TimePickerCtrl(datetime_panel)
        self.time_picker.SetValue(wx.DateTime.Now())
        self.time_picker.Enable(False)
        datetime_sizer.Add(self.time_picker, 0, wx.EXPAND)

        datetime_panel.SetSizer(datetime_sizer)
        due_main_sizer.Add(datetime_panel, 0, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, UIStyles.SPACING['xs'])

        due_panel.SetSizer(due_main_sizer)
        form_sizer.Add(due_panel, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['xs'])

        form_panel.SetSizer(form_sizer)
        return form_panel
    
    def create_button_panel(self, parent) -> wx.Panel:
        """创建按钮面板"""
        button_panel = wx.Panel(parent)
        button_panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.theme))
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # 添加弹性空间
        button_sizer.AddStretchSpacer()
        
        # 取消按钮
        cancel_btn = wx.Button(button_panel, wx.ID_CANCEL, "取消", size=(100, 35))
        cancel_btn.SetFont(UIStyles.get_font('medium'))
        button_sizer.Add(cancel_btn, 0, wx.RIGHT, UIStyles.SPACING['sm'])
        
        # 确定按钮
        ok_btn = wx.Button(button_panel, wx.ID_OK, "创建", size=(100, 35))
        ok_btn.SetFont(UIStyles.get_font('medium', bold=True))
        ok_btn.SetDefault()
        button_sizer.Add(ok_btn, 0)
        
        button_panel.SetSizer(button_sizer)
        return button_panel
    
    def setup_events(self) -> None:
        """设置事件处理"""
        # 到期时间复选框事件
        self.due_checkbox.Bind(wx.EVT_CHECKBOX, self.on_due_checkbox)
        
        # 按钮事件
        self.Bind(wx.EVT_BUTTON, self.on_ok, id=wx.ID_OK)
        self.Bind(wx.EVT_BUTTON, self.on_cancel, id=wx.ID_CANCEL)
    
    def on_due_checkbox(self, event) -> None:
        """到期时间复选框事件"""
        enabled = self.due_checkbox.GetValue()
        self.date_picker.Enable(enabled)
        self.time_picker.Enable(enabled)
    
    def on_ok(self, event) -> None:
        """确定按钮事件"""
        if self.validate_input():
            self.collect_data()
            self.EndModal(wx.ID_OK)
    
    def on_cancel(self, event) -> None:
        """取消按钮事件"""
        self.EndModal(wx.ID_CANCEL)
    
    def validate_input(self) -> bool:
        """验证输入"""
        title = self.title_ctrl.GetValue().strip()
        if not title:
            wx.MessageBox("请输入任务标题", "输入错误", wx.OK | wx.ICON_WARNING)
            self.title_ctrl.SetFocus()
            return False
        
        return True
    
    def collect_data(self) -> None:
        """收集表单数据"""
        # 基本信息
        self.todo_data['title'] = self.title_ctrl.GetValue().strip()
        self.todo_data['description'] = self.desc_ctrl.GetValue().strip()
        
        # 优先级 (1=低, 2=中, 3=高)
        self.todo_data['priority'] = self.priority_choice.GetSelection() + 1
        
        # 分类ID
        category_selection = self.category_choice.GetSelection()
        if category_selection > 0:  # 0是"无"
            self.todo_data['category_id'] = self.categories[category_selection - 1].id
        else:
            self.todo_data['category_id'] = None
        
        # 文件夹ID
        folder_selection = self.folder_choice.GetSelection()
        if folder_selection > 0:  # 0是"无"
            self.todo_data['folder_id'] = self.folders[folder_selection - 1].id
        else:
            self.todo_data['folder_id'] = None
        
        # 到期时间
        if self.due_checkbox.GetValue():
            date = self.date_picker.GetValue()
            time = self.time_picker.GetValue()
            
            # 组合日期和时间
            due_datetime = datetime(
                date.GetYear(),
                date.GetMonth() + 1,  # wx.DateTime的月份从0开始
                date.GetDay(),
                time.GetHour(),
                time.GetMinute()
            )
            self.todo_data['due_date'] = due_datetime
        else:
            self.todo_data['due_date'] = None
    
    def get_todo_data(self) -> Dict[str, Any]:
        """获取任务数据"""
        return self.todo_data
    
    def set_categories(self, categories) -> None:
        """设置分类列表"""
        self.categories = categories
        category_choices = ["无"] + [cat.name for cat in self.categories]
        self.category_choice.SetItems(category_choices)
        self.category_choice.SetSelection(0)
    
    def set_folders(self, folders) -> None:
        """设置文件夹列表"""
        self.folders = folders
        folder_choices = ["无"] + [folder.name for folder in self.folders]
        self.folder_choice.SetItems(folder_choices)
        self.folder_choice.SetSelection(0)
