"""
最新修复功能测试程序
"""
import wx
import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟数据模型
class MockCategory:
    def __init__(self, id, name, color="#3498db", icon="💼"):
        self.id = id
        self.name = name
        self.color = color
        self.icon = icon

class MockFolder:
    def __init__(self, id, name, color="#95a5a6", parent_id=None):
        self.id = id
        self.name = name
        self.color = color
        self.parent_id = parent_id

class MockCategoryManager:
    def __init__(self):
        self.categories = [
            MockCategory(1, "工作", "#e74c3c", "💼"),
            MockCategory(2, "个人", "#2ecc71", "👤"),
        ]
        self.next_id = 3
    
    def get_categories(self):
        return self.categories
    
    def add_category(self, name, color="#3498db", icon="💼"):
        new_category = MockCategory(self.next_id, name, color, icon)
        self.categories.append(new_category)
        self.next_id += 1
        return new_category.id
    
    def update_category(self, category_id, name=None, color=None, icon=None):
        for category in self.categories:
            if category.id == category_id:
                if name: category.name = name
                if color: category.color = color
                if icon: category.icon = icon
                return True
        return False

class MockFolderManager:
    def __init__(self):
        self.folders = [
            MockFolder(1, "重要", "#e74c3c"),
            MockFolder(2, "今天", "#f39c12"),
        ]
        self.next_id = 3
    
    def get_folders(self):
        return self.folders


class LatestFixTestWindow(wx.Frame):
    """最新修复功能测试窗口"""
    
    def __init__(self):
        super().__init__(None, title="Todo List 最新修复功能测试", size=(900, 700))
        
        # 模拟管理器
        self.category_manager = MockCategoryManager()
        self.folder_manager = MockFolderManager()
        
        self.current_theme = 'light'
        
        self.init_ui()
        self.Center()
    
    def init_ui(self):
        """初始化用户界面"""
        panel = wx.Panel(self)
        panel.SetBackgroundColour(wx.Colour("#f8f9fa"))
        
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题
        title = wx.StaticText(panel, label="Todo List 最新修复功能测试")
        title.SetFont(wx.Font(16, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        sizer.Add(title, 0, wx.ALL | wx.CENTER, 20)
        
        # 测试按钮网格
        button_sizer = wx.GridSizer(3, 3, 10, 10)
        
        # 分类保存测试
        category_save_btn = wx.Button(panel, label="测试分类立即保存", size=(180, 50))
        category_save_btn.Bind(wx.EVT_BUTTON, self.test_category_save)
        button_sizer.Add(category_save_btn, 0, wx.EXPAND)
        
        category_dialog_btn = wx.Button(panel, label="测试分类管理对话框", size=(180, 50))
        category_dialog_btn.Bind(wx.EVT_BUTTON, self.test_category_dialog)
        button_sizer.Add(category_dialog_btn, 0, wx.EXPAND)
        
        # 新建任务布局测试
        add_dialog_btn = wx.Button(panel, label="测试新建任务对话框", size=(180, 50))
        add_dialog_btn.Bind(wx.EVT_BUTTON, self.test_add_dialog)
        button_sizer.Add(add_dialog_btn, 0, wx.EXPAND)
        
        layout_test_btn = wx.Button(panel, label="测试横向布局", size=(180, 50))
        layout_test_btn.Bind(wx.EVT_BUTTON, self.test_layout)
        button_sizer.Add(layout_test_btn, 0, wx.EXPAND)
        
        # 文件夹多选测试
        multiselect_btn = wx.Button(panel, label="测试文件夹多选", size=(180, 50))
        multiselect_btn.Bind(wx.EVT_BUTTON, self.test_multiselect)
        button_sizer.Add(multiselect_btn, 0, wx.EXPAND)
        
        tree_test_btn = wx.Button(panel, label="测试树控件多选", size=(180, 50))
        tree_test_btn.Bind(wx.EVT_BUTTON, self.test_tree_multiselect)
        button_sizer.Add(tree_test_btn, 0, wx.EXPAND)
        
        # 综合测试
        comprehensive_btn = wx.Button(panel, label="综合测试", size=(180, 50))
        comprehensive_btn.Bind(wx.EVT_BUTTON, self.test_comprehensive)
        button_sizer.Add(comprehensive_btn, 0, wx.EXPAND)
        
        # 清空数据
        clear_btn = wx.Button(panel, label="清空测试数据", size=(180, 50))
        clear_btn.Bind(wx.EVT_BUTTON, self.clear_test_data)
        button_sizer.Add(clear_btn, 0, wx.EXPAND)
        
        # 显示状态
        status_btn = wx.Button(panel, label="显示状态", size=(180, 50))
        status_btn.Bind(wx.EVT_BUTTON, self.show_status)
        button_sizer.Add(status_btn, 0, wx.EXPAND)
        
        sizer.Add(button_sizer, 0, wx.ALL | wx.CENTER, 20)
        
        # 结果显示区域
        self.result_text = wx.TextCtrl(panel, style=wx.TE_MULTILINE | wx.TE_READONLY, size=(-1, 300))
        self.result_text.SetFont(wx.Font(9, wx.FONTFAMILY_TELETYPE, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        sizer.Add(self.result_text, 1, wx.EXPAND | wx.ALL, 20)
        
        panel.SetSizer(sizer)
    
    def log_result(self, test_name, result, details=""):
        """记录测试结果"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        status = "✅ 成功" if result else "❌ 失败"
        log_text = f"[{timestamp}] {test_name}: {status}\n"
        if details:
            log_text += f"详情: {details}\n"
        log_text += "-" * 50 + "\n"
        
        self.result_text.AppendText(log_text)
    
    def test_category_save(self, event):
        """测试分类立即保存功能"""
        try:
            # 模拟分类保存
            initial_count = len(self.category_manager.get_categories())
            category_id = self.category_manager.add_category("测试分类", "#ff6b6b", "🧪")
            final_count = len(self.category_manager.get_categories())
            
            success = category_id > 0 and final_count > initial_count
            self.log_result("分类立即保存", success, 
                          f"初始: {initial_count}, 新ID: {category_id}, 最终: {final_count}")
        except Exception as e:
            self.log_result("分类立即保存", False, str(e))
    
    def test_category_dialog(self, event):
        """测试分类管理对话框"""
        try:
            from ui.components.category_manager_dialog import CategoryManagerDialog
            categories = self.category_manager.get_categories()
            dlg = CategoryManagerDialog(self, categories, self.current_theme)
            
            # 检查对话框是否有立即保存功能
            has_save_btn = False
            for child in dlg.GetChildren():
                if self._find_save_button(child):
                    has_save_btn = True
                    break
            
            result = dlg.ShowModal()
            dlg.Destroy()
            
            self.log_result("分类管理对话框", True, 
                          f"对话框显示正常, 有保存按钮: {has_save_btn}, 结果: {result}")
        except Exception as e:
            self.log_result("分类管理对话框", False, str(e))
    
    def _find_save_button(self, widget):
        """递归查找保存按钮"""
        if isinstance(widget, wx.Button) and "保存" in widget.GetLabel():
            return True
        
        if hasattr(widget, 'GetChildren'):
            for child in widget.GetChildren():
                if self._find_save_button(child):
                    return True
        return False
    
    def test_add_dialog(self, event):
        """测试新建任务对话框"""
        try:
            from ui.components.add_todo_dialog import AddTodoDialog
            categories = self.category_manager.get_categories()
            folders = self.folder_manager.get_folders()
            
            dlg = AddTodoDialog(self, categories, folders, self.current_theme)
            size = dlg.GetSize()
            min_size = dlg.GetMinSize()
            
            # 检查是否是横向布局（宽度应该大于高度）
            is_horizontal = size[0] > size[1]
            
            result = dlg.ShowModal()
            dlg.Destroy()
            
            self.log_result("新建任务对话框", True, 
                          f"尺寸: {size}, 最小: {min_size}, 横向布局: {is_horizontal}, 结果: {result}")
        except Exception as e:
            self.log_result("新建任务对话框", False, str(e))
    
    def test_layout(self, event):
        """测试横向布局"""
        try:
            from ui.components.add_todo_dialog import AddTodoDialog
            dlg = AddTodoDialog(self, [], [], self.current_theme)
            
            size = dlg.GetSize()
            min_size = dlg.GetMinSize()
            
            # 检查布局特征
            width_ok = size[0] >= 750  # 宽度应该足够
            height_ok = size[1] <= 600  # 高度应该适中
            aspect_ratio = size[0] / size[1]
            is_horizontal = aspect_ratio > 1.3  # 宽高比大于1.3认为是横向
            
            dlg.Destroy()
            
            all_ok = width_ok and height_ok and is_horizontal
            details = f"尺寸: {size}, 宽度{'✓' if width_ok else '✗'}, 高度{'✓' if height_ok else '✗'}, 横向{'✓' if is_horizontal else '✗'}, 比例: {aspect_ratio:.2f}"
            
            self.log_result("横向布局", all_ok, details)
        except Exception as e:
            self.log_result("横向布局", False, str(e))
    
    def test_multiselect(self, event):
        """测试文件夹多选功能"""
        try:
            # 创建一个简单的树控件测试多选
            test_frame = wx.Frame(self, title="多选测试", size=(400, 300))
            panel = wx.Panel(test_frame)
            
            tree = wx.TreeCtrl(panel, style=wx.TR_DEFAULT_STYLE | wx.TR_MULTIPLE)
            
            # 添加测试数据
            root = tree.AddRoot("文件夹")
            item1 = tree.AppendItem(root, "文件夹1")
            item2 = tree.AppendItem(root, "文件夹2")
            item3 = tree.AppendItem(root, "文件夹3")
            
            tree.Expand(root)
            
            # 测试多选
            tree.SelectItem(item1, True)
            tree.SelectItem(item2, True)
            
            selections = tree.GetSelections()
            
            sizer = wx.BoxSizer(wx.VERTICAL)
            sizer.Add(tree, 1, wx.EXPAND | wx.ALL, 10)
            panel.SetSizer(sizer)
            
            test_frame.Show()
            
            self.log_result("文件夹多选", True, f"多选功能正常, 选中项数: {len(selections)}")
        except Exception as e:
            self.log_result("文件夹多选", False, str(e))
    
    def test_tree_multiselect(self, event):
        """测试树控件多选样式"""
        try:
            # 测试不同的树控件样式
            styles = [
                ("单选", wx.TR_DEFAULT_STYLE | wx.TR_SINGLE),
                ("多选", wx.TR_DEFAULT_STYLE | wx.TR_MULTIPLE),
            ]
            
            results = []
            for name, style in styles:
                try:
                    tree = wx.TreeCtrl(self, style=style)
                    root = tree.AddRoot("测试")
                    tree.AppendItem(root, "项目1")
                    tree.AppendItem(root, "项目2")
                    
                    # 检查样式
                    has_multiple = bool(style & wx.TR_MULTIPLE)
                    results.append(f"{name}: {'支持' if has_multiple else '不支持'}多选")
                    
                    tree.Destroy()
                except Exception as e:
                    results.append(f"{name}: 错误 - {e}")
            
            self.log_result("树控件多选样式", True, "; ".join(results))
        except Exception as e:
            self.log_result("树控件多选样式", False, str(e))
    
    def test_comprehensive(self, event):
        """综合测试"""
        self.log_result("综合测试", True, "开始综合测试...")
        
        # 依次执行各项测试
        wx.CallLater(100, self._comprehensive_step_1)
    
    def _comprehensive_step_1(self):
        self.test_category_save(None)
        wx.CallLater(500, self._comprehensive_step_2)
    
    def _comprehensive_step_2(self):
        self.test_layout(None)
        wx.CallLater(500, self._comprehensive_step_3)
    
    def _comprehensive_step_3(self):
        self.test_tree_multiselect(None)
        self.log_result("综合测试", True, "综合测试完成！")
    
    def clear_test_data(self, event):
        """清空测试数据"""
        self.category_manager = MockCategoryManager()
        self.folder_manager = MockFolderManager()
        self.log_result("清空测试数据", True, "所有测试数据已清空")
    
    def show_status(self, event):
        """显示状态"""
        categories = self.category_manager.get_categories()
        folders = self.folder_manager.get_folders()
        
        cat_info = [f"{c.name}({c.icon})" for c in categories]
        folder_info = [f.name for f in folders]
        
        details = f"分类({len(categories)}): {cat_info}, 文件夹({len(folders)}): {folder_info}"
        self.log_result("状态显示", True, details)


class LatestFixTestApp(wx.App):
    """最新修复测试应用"""
    
    def OnInit(self):
        try:
            self.main_window = LatestFixTestWindow()
            self.main_window.Show()
            self.SetTopWindow(self.main_window)
            return True
        except Exception as e:
            wx.MessageBox(f"应用启动失败: {str(e)}", "错误", wx.OK | wx.ICON_ERROR)
            return False


def main():
    """主函数"""
    app = LatestFixTestApp()
    app.MainLoop()


if __name__ == "__main__":
    main()
