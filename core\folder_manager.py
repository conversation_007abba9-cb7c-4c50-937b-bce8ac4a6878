"""
文件夹管理器
"""
from typing import List, Optional, Dict
from .database import DatabaseManager
from .models import Folder


class FolderManager:
    """文件夹管理类"""
    
    def __init__(self, db_manager: DatabaseManager):
        """初始化文件夹管理器"""
        self.db = db_manager
    
    def add_folder(self, name: str, parent_id: int = None, color: str = '#95a5a6') -> int:
        """添加文件夹"""
        # 验证文件夹名称
        if not name or not name.strip():
            raise ValueError("文件夹名称不能为空")
        
        # 检查父文件夹是否存在
        if parent_id is not None:
            parent_folder = self.get_folder_by_id(parent_id)
            if not parent_folder:
                raise ValueError(f"父文件夹ID {parent_id} 不存在")
        
        # 检查是否已存在同名文件夹（在同一父级下）
        if self.folder_exists(name.strip(), parent_id):
            parent_name = "根目录" if parent_id is None else self.get_folder_by_id(parent_id).name
            raise ValueError(f"在 '{parent_name}' 下已存在文件夹 '{name}'")
        
        return self.db.add_folder(name.strip(), parent_id, color)
    
    def update_folder(self, folder_id: int, name: str = None, 
                     parent_id: int = None, color: str = None) -> bool:
        """更新文件夹"""
        # 验证文件夹是否存在
        folder = self.get_folder_by_id(folder_id)
        if not folder:
            raise ValueError(f"文件夹ID {folder_id} 不存在")
        
        # 如果更新父文件夹，检查是否会造成循环引用
        if parent_id is not None and parent_id != folder.parent_id:
            if self._would_create_cycle(folder_id, parent_id):
                raise ValueError("不能将文件夹移动到其子文件夹下，这会造成循环引用")
        
        # 如果更新名称，检查是否与同级文件夹重名
        if name and name.strip():
            check_parent_id = parent_id if parent_id is not None else folder.parent_id
            existing_folders = self.get_folders_by_parent(check_parent_id)
            for f in existing_folders:
                if f.id != folder_id and f.name.lower() == name.strip().lower():
                    parent_name = "根目录" if check_parent_id is None else self.get_folder_by_id(check_parent_id).name
                    raise ValueError(f"在 '{parent_name}' 下已存在文件夹 '{name}'")
        
        return self.db.update_folder(folder_id, name, parent_id, color)
    
    def delete_folder(self, folder_id: int) -> bool:
        """删除文件夹"""
        # 验证文件夹是否存在
        folder = self.get_folder_by_id(folder_id)
        if not folder:
            raise ValueError(f"文件夹ID {folder_id} 不存在")
        
        # 检查是否有子文件夹
        child_folders = self.get_folders_by_parent(folder_id)
        if child_folders:
            print(f"警告: 文件夹 '{folder.name}' 下有 {len(child_folders)} 个子文件夹，删除后这些子文件夹将移动到根目录")
        
        # 检查是否有任务使用此文件夹
        todos_count = self._get_todos_count_by_folder(folder_id)
        if todos_count > 0:
            print(f"警告: 文件夹 '{folder.name}' 下有 {todos_count} 个任务，删除后这些任务将变为无文件夹")
        
        return self.db.delete_folder(folder_id)
    
    def get_folders(self) -> List[Folder]:
        """获取所有文件夹"""
        return self.db.get_folders()
    
    def get_folder_by_id(self, folder_id: int) -> Optional[Folder]:
        """根据ID获取文件夹"""
        return self.db.get_folder_by_id(folder_id)

    def get_folder_by_name(self, name: str, parent_id: int = None) -> Optional[Folder]:
        """根据名称获取文件夹（在指定父文件夹下）"""
        folders = self.get_folders_by_parent(parent_id)
        for folder in folders:
            if folder.name.lower() == name.lower():
                return folder
        return None

    def folder_exists(self, name: str, parent_id: int = None) -> bool:
        """检查文件夹是否存在（在指定父文件夹下）"""
        return self.get_folder_by_name(name, parent_id) is not None
    
    def get_folders_by_parent(self, parent_id: int = None) -> List[Folder]:
        """获取指定父文件夹下的子文件夹"""
        all_folders = self.get_folders()
        return [f for f in all_folders if f.parent_id == parent_id]
    
    def get_root_folders(self) -> List[Folder]:
        """获取根级文件夹"""
        return self.get_folders_by_parent(None)
    
    def get_folder_tree(self) -> List[Dict]:
        """获取文件夹树结构"""
        def build_tree(parent_id=None):
            folders = self.get_folders_by_parent(parent_id)
            tree = []
            for folder in folders:
                folder_data = {
                    'folder': folder,
                    'children': build_tree(folder.id),
                    'task_count': self._get_todos_count_by_folder(folder.id)
                }
                tree.append(folder_data)
            return tree
        
        return build_tree()
    
    def get_folder_path(self, folder_id: int) -> str:
        """获取文件夹的完整路径"""
        folder = self.get_folder_by_id(folder_id)
        if not folder:
            return ""
        
        path_parts = [folder.name]
        current_folder = folder
        
        while current_folder.parent_id is not None:
            parent_folder = self.get_folder_by_id(current_folder.parent_id)
            if parent_folder:
                path_parts.insert(0, parent_folder.name)
                current_folder = parent_folder
            else:
                break
        
        return " / ".join(path_parts)
    
    def move_folder(self, folder_id: int, new_parent_id: int = None) -> bool:
        """移动文件夹到新的父文件夹"""
        return self.update_folder(folder_id, parent_id=new_parent_id)
    
    def _would_create_cycle(self, folder_id: int, new_parent_id: int) -> bool:
        """检查移动文件夹是否会造成循环引用"""
        if new_parent_id is None:
            return False
        
        # 检查new_parent_id是否是folder_id的子孙文件夹
        current_id = new_parent_id
        while current_id is not None:
            if current_id == folder_id:
                return True
            
            folder = self.get_folder_by_id(current_id)
            if folder:
                current_id = folder.parent_id
            else:
                break
        
        return False
    
    def _get_todos_count_by_folder(self, folder_id: int) -> int:
        """获取指定文件夹下的任务数量"""
        query = "SELECT COUNT(*) as count FROM todos WHERE folder_id = ?"
        rows = self.db.execute_query(query, (folder_id,))
        return rows[0]['count'] if rows else 0
    
    def create_default_folders(self) -> int:
        """创建默认文件夹，返回创建的文件夹数量"""
        default_folders = [
            {'name': '重要', 'color': '#e74c3c'},
            {'name': '今天', 'color': '#f39c12'},
            {'name': '本周', 'color': '#3498db'},
            {'name': '下周', 'color': '#9b59b6'},
            {'name': '稍后', 'color': '#95a5a6'}
        ]

        created_count = 0

        # 获取现有的根级文件夹
        existing_folders = self.get_root_folders()
        existing_names = {f.name.lower() for f in existing_folders}

        print(f"检查默认文件夹创建，现有文件夹: {[f.name for f in existing_folders]}")

        for folder_data in default_folders:
            folder_name_lower = folder_data['name'].lower()

            if folder_name_lower not in existing_names:
                try:
                    folder_id = self.add_folder(folder_data['name'], None, folder_data['color'])
                    if folder_id > 0:
                        created_count += 1
                        print(f"创建默认文件夹: {folder_data['name']} (ID: {folder_id})")
                    else:
                        print(f"创建默认文件夹失败: {folder_data['name']}")
                except ValueError as e:
                    print(f"创建默认文件夹异常: {folder_data['name']} - {e}")
                    continue
            else:
                print(f"默认文件夹已存在，跳过: {folder_data['name']}")

        print(f"默认文件夹创建完成，新创建: {created_count} 个")
        return created_count
    
    def export_folders(self) -> List[dict]:
        """导出文件夹数据"""
        folders = self.get_folders()
        return [
            {
                'name': f.name,
                'parent_name': self.get_folder_by_id(f.parent_id).name if f.parent_id else None,
                'color': f.color
            }
            for f in folders
        ]
    
    def validate_folder_data(self, name: str, parent_id: int = None, color: str = None) -> dict:
        """验证文件夹数据"""
        errors = []
        
        # 验证名称
        if not name or not name.strip():
            errors.append("文件夹名称不能为空")
        elif len(name.strip()) > 50:
            errors.append("文件夹名称不能超过50个字符")
        
        # 验证父文件夹
        if parent_id is not None:
            parent_folder = self.get_folder_by_id(parent_id)
            if not parent_folder:
                errors.append(f"父文件夹ID {parent_id} 不存在")
        
        # 验证颜色
        if color and not color.startswith('#'):
            errors.append("颜色格式不正确，应为十六进制格式（如#95a5a6）")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
