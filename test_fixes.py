"""
修复功能测试程序
"""
import wx
import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟数据模型和管理器
class MockTodo:
    def __init__(self, id=1, title="测试任务", description="这是一个测试任务", 
                 priority=2, category_id=1, folder_id=1, due_date=None, completed=False):
        self.id = id
        self.title = title
        self.description = description
        self.priority = priority
        self.category_id = category_id
        self.folder_id = folder_id
        self.due_date = due_date
        self.completed = completed
    
    @property
    def priority_text(self):
        return {1: "低", 2: "中", 3: "高"}.get(self.priority, "低")
    
    @property
    def priority_color(self):
        return {1: "#95a5a6", 2: "#f39c12", 3: "#e74c3c"}.get(self.priority, "#95a5a6")
    
    @property
    def is_overdue(self):
        if self.due_date and not self.completed:
            return datetime.now() > self.due_date
        return False

class MockCategory:
    def __init__(self, id, name, color="#3498db", icon="💼"):
        self.id = id
        self.name = name
        self.color = color
        self.icon = icon

class MockFolder:
    def __init__(self, id, name, color="#95a5a6"):
        self.id = id
        self.name = name
        self.color = color

class MockTodoManager:
    def __init__(self):
        self.todos = [
            MockTodo(1, "完成项目报告", "需要在周五前完成", 3, 1, 1, datetime.now() + timedelta(days=2)),
            MockTodo(2, "购买生活用品", "牛奶、面包、鸡蛋", 1, 4, 2),
            MockTodo(3, "学习Python", "完成第5章练习", 2, 3, 3),
            MockTodo(4, "健身锻炼", "跑步30分钟", 2, 2, 1),
            MockTodo(5, "已完成任务", "这是一个已完成的任务", 1, 1, 1, completed=True)
        ]
    
    def get_todos(self, filters=None):
        if filters is None:
            return self.todos
        
        result = self.todos
        if 'completed' in filters:
            result = [t for t in result if t.completed == filters['completed']]
        if 'category_id' in filters:
            result = [t for t in result if t.category_id == filters['category_id']]
        if 'folder_id' in filters:
            result = [t for t in result if t.folder_id == filters['folder_id']]
        
        return result
    
    def add_todo(self, title, description="", priority=1, category_id=None, folder_id=None, due_date=None):
        new_id = max([t.id for t in self.todos]) + 1 if self.todos else 1
        new_todo = MockTodo(new_id, title, description, priority, category_id, folder_id, due_date)
        self.todos.append(new_todo)
        return new_id
    
    def mark_completed(self, todo_id, completed):
        for todo in self.todos:
            if todo.id == todo_id:
                todo.completed = completed
                return True
        return False

class MockCategoryManager:
    def __init__(self):
        self.categories = [
            MockCategory(1, "工作", "#e74c3c", "💼"),
            MockCategory(2, "个人", "#2ecc71", "👤"),
            MockCategory(3, "学习", "#f39c12", "📚"),
            MockCategory(4, "购物", "#9b59b6", "🛒")
        ]
    
    def get_categories(self):
        return self.categories
    
    def add_category(self, name, color="#3498db", icon="💼"):
        new_id = max([c.id for c in self.categories]) + 1 if self.categories else 1
        new_category = MockCategory(new_id, name, color, icon)
        self.categories.append(new_category)
        return new_id
    
    def delete_category(self, category_id):
        self.categories = [c for c in self.categories if c.id != category_id]
        return True

class MockFolderManager:
    def __init__(self):
        self.folders = [
            MockFolder(1, "重要", "#e74c3c"),
            MockFolder(2, "今天", "#f39c12"),
            MockFolder(3, "本周", "#3498db")
        ]
    
    def get_folders(self):
        return self.folders
    
    def get_folder_tree(self):
        return [{'folder': f, 'children': [], 'task_count': 2} for f in self.folders]
    
    def add_folder(self, name, parent_id=None, color="#95a5a6"):
        new_id = max([f.id for f in self.folders]) + 1 if self.folders else 1
        new_folder = MockFolder(new_id, name, color)
        self.folders.append(new_folder)
        return new_id
    
    def delete_folder(self, folder_id):
        self.folders = [f for f in self.folders if f.id != folder_id]
        return True


class FixTestWindow(wx.Frame):
    """修复功能测试窗口"""
    
    def __init__(self):
        super().__init__(None, title="Todo List 修复功能测试", size=(900, 700))
        
        # 模拟管理器
        self.todo_manager = MockTodoManager()
        self.category_manager = MockCategoryManager()
        self.folder_manager = MockFolderManager()
        
        self.current_theme = 'light'
        self.floating_window = None
        
        self.init_ui()
        self.Center()
    
    def init_ui(self):
        """初始化用户界面"""
        panel = wx.Panel(self)
        panel.SetBackgroundColour(wx.Colour("#f8f9fa"))
        
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题
        title = wx.StaticText(panel, label="Todo List 修复功能测试")
        title.SetFont(wx.Font(16, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        sizer.Add(title, 0, wx.ALL | wx.CENTER, 20)
        
        # 测试按钮网格
        button_sizer = wx.GridSizer(3, 3, 10, 10)
        
        # 分类管理测试
        cat_add_btn = wx.Button(panel, label="测试添加分类", size=(150, 50))
        cat_add_btn.Bind(wx.EVT_BUTTON, self.test_add_category)
        button_sizer.Add(cat_add_btn, 0, wx.EXPAND)
        
        cat_del_btn = wx.Button(panel, label="测试删除分类", size=(150, 50))
        cat_del_btn.Bind(wx.EVT_BUTTON, self.test_delete_category)
        button_sizer.Add(cat_del_btn, 0, wx.EXPAND)
        
        cat_mgr_btn = wx.Button(panel, label="测试分类管理", size=(150, 50))
        cat_mgr_btn.Bind(wx.EVT_BUTTON, self.test_category_manager)
        button_sizer.Add(cat_mgr_btn, 0, wx.EXPAND)
        
        # 文件夹管理测试
        folder_add_btn = wx.Button(panel, label="测试添加文件夹", size=(150, 50))
        folder_add_btn.Bind(wx.EVT_BUTTON, self.test_add_folder)
        button_sizer.Add(folder_add_btn, 0, wx.EXPAND)
        
        folder_del_btn = wx.Button(panel, label="测试删除文件夹", size=(150, 50))
        folder_del_btn.Bind(wx.EVT_BUTTON, self.test_delete_folder)
        button_sizer.Add(folder_del_btn, 0, wx.EXPAND)
        
        # 主题测试
        theme_light_btn = wx.Button(panel, label="切换到明亮主题", size=(150, 50))
        theme_light_btn.Bind(wx.EVT_BUTTON, lambda evt: self.test_theme_switch('light'))
        button_sizer.Add(theme_light_btn, 0, wx.EXPAND)
        
        theme_dark_btn = wx.Button(panel, label="切换到暗黑主题", size=(150, 50))
        theme_dark_btn.Bind(wx.EVT_BUTTON, lambda evt: self.test_theme_switch('dark'))
        button_sizer.Add(theme_dark_btn, 0, wx.EXPAND)
        
        # 悬浮窗测试
        floating_btn = wx.Button(panel, label="测试悬浮窗模式", size=(150, 50))
        floating_btn.Bind(wx.EVT_BUTTON, self.test_floating_window)
        button_sizer.Add(floating_btn, 0, wx.EXPAND)
        
        # 设置对话框测试
        settings_btn = wx.Button(panel, label="测试设置对话框", size=(150, 50))
        settings_btn.Bind(wx.EVT_BUTTON, self.test_settings_dialog)
        button_sizer.Add(settings_btn, 0, wx.EXPAND)
        
        sizer.Add(button_sizer, 0, wx.ALL | wx.CENTER, 20)
        
        # 结果显示区域
        self.result_text = wx.TextCtrl(panel, style=wx.TE_MULTILINE | wx.TE_READONLY, size=(-1, 300))
        self.result_text.SetFont(wx.Font(9, wx.FONTFAMILY_TELETYPE, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        sizer.Add(self.result_text, 1, wx.EXPAND | wx.ALL, 20)
        
        panel.SetSizer(sizer)
    
    def log_result(self, test_name, result, details=""):
        """记录测试结果"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        status = "✅ 成功" if result else "❌ 失败"
        log_text = f"[{timestamp}] {test_name}: {status}\n"
        if details:
            log_text += f"详情: {details}\n"
        log_text += "-" * 50 + "\n"
        
        self.result_text.AppendText(log_text)
    
    def test_add_category(self, event):
        """测试添加分类"""
        try:
            category_id = self.category_manager.add_category("测试分类", "#ff6b6b", "🧪")
            self.log_result("添加分类", True, f"新分类ID: {category_id}")
        except Exception as e:
            self.log_result("添加分类", False, str(e))
    
    def test_delete_category(self, event):
        """测试删除分类"""
        try:
            categories = self.category_manager.get_categories()
            if categories:
                result = self.category_manager.delete_category(categories[-1].id)
                self.log_result("删除分类", result, f"删除分类: {categories[-1].name}")
            else:
                self.log_result("删除分类", False, "没有可删除的分类")
        except Exception as e:
            self.log_result("删除分类", False, str(e))
    
    def test_category_manager(self, event):
        """测试分类管理对话框"""
        try:
            from ui.components.category_manager_dialog import CategoryManagerDialog
            categories = self.category_manager.get_categories()
            dlg = CategoryManagerDialog(self, categories, self.current_theme)
            result = dlg.ShowModal()
            dlg.Destroy()
            self.log_result("分类管理对话框", True, f"对话框结果: {result}")
        except Exception as e:
            self.log_result("分类管理对话框", False, str(e))
    
    def test_add_folder(self, event):
        """测试添加文件夹"""
        try:
            folder_id = self.folder_manager.add_folder("测试文件夹", None, "#4ecdc4")
            self.log_result("添加文件夹", True, f"新文件夹ID: {folder_id}")
        except Exception as e:
            self.log_result("添加文件夹", False, str(e))
    
    def test_delete_folder(self, event):
        """测试删除文件夹"""
        try:
            folders = self.folder_manager.get_folders()
            if folders:
                result = self.folder_manager.delete_folder(folders[-1].id)
                self.log_result("删除文件夹", result, f"删除文件夹: {folders[-1].name}")
            else:
                self.log_result("删除文件夹", False, "没有可删除的文件夹")
        except Exception as e:
            self.log_result("删除文件夹", False, str(e))
    
    def test_theme_switch(self, theme):
        """测试主题切换"""
        try:
            old_theme = self.current_theme
            self.current_theme = theme
            
            # 应用主题到当前窗口
            from ui.styles import UIStyles
            UIStyles.set_theme(theme)
            
            # 更新窗口颜色
            if theme == 'dark':
                self.SetBackgroundColour(wx.Colour("#2c3e50"))
            else:
                self.SetBackgroundColour(wx.Colour("#f8f9fa"))
            
            self.Refresh()
            
            self.log_result("主题切换", True, f"{old_theme} -> {theme}")
        except Exception as e:
            self.log_result("主题切换", False, str(e))
    
    def test_floating_window(self, event):
        """测试悬浮窗"""
        try:
            if not self.floating_window:
                from utils.system_integration import FloatingWindow
                self.floating_window = FloatingWindow(self, self.todo_manager, self.current_theme)
                self.floating_window.Show()
                self.log_result("悬浮窗", True, "悬浮窗已创建并显示")
            else:
                self.floating_window.Close()
                self.floating_window = None
                self.log_result("悬浮窗", True, "悬浮窗已关闭")
        except Exception as e:
            self.log_result("悬浮窗", False, str(e))
    
    def test_settings_dialog(self, event):
        """测试设置对话框"""
        try:
            from ui.components.settings_dialog import SettingsDialog
            current_settings = {'theme': self.current_theme}
            dlg = SettingsDialog(self, current_settings, self.current_theme)
            result = dlg.ShowModal()
            if result == wx.ID_OK:
                settings = dlg.get_settings_data()
                self.log_result("设置对话框", True, f"新设置: {settings.get('theme', 'unknown')}")
            else:
                self.log_result("设置对话框", True, "用户取消")
            dlg.Destroy()
        except Exception as e:
            self.log_result("设置对话框", False, str(e))


class FixTestApp(wx.App):
    """修复测试应用"""
    
    def OnInit(self):
        try:
            self.main_window = FixTestWindow()
            self.main_window.Show()
            self.SetTopWindow(self.main_window)
            return True
        except Exception as e:
            wx.MessageBox(f"应用启动失败: {str(e)}", "错误", wx.OK | wx.ICON_ERROR)
            return False


def main():
    """主函数"""
    app = FixTestApp()
    app.MainLoop()


if __name__ == "__main__":
    main()
