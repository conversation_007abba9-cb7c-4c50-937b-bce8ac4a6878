"""
主窗口
"""
import wx
import wx.lib.agw.aui as aui
from datetime import datetime
from typing import Optional, List
from ui.styles import UIStyles
from ui.animations import AnimationManager
from ui.components.todo_card import TodoCard
from ui.components.add_todo_dialog import AddTodoDialog
from ui.components.edit_todo_dialog import EditTodoDialog
from ui.components.settings_dialog import SettingsDialog
from ui.components.category_manager_dialog import CategoryManagerDialog
from core.database import DatabaseManager
from core.todo_manager import TodoManager
from core.notification import NotificationManager
from core.category_manager import CategoryManager
from core.folder_manager import FolderManager
from utils.system_integration import SystemIntegration, FloatingWindow
from utils.audio_manager import AudioManager


class MainWindow(wx.Frame):
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__(None, title="Todo List", size=(1000, 700))
        
        # 初始化核心组件
        self.db_manager = DatabaseManager()
        self.todo_manager = TodoManager(self.db_manager)
        self.category_manager = CategoryManager(self.db_manager)
        self.folder_manager = FolderManager(self.db_manager)
        self.notification_manager = NotificationManager()
        self.animation_manager = AnimationManager()
        self.system_integration = SystemIntegration("Todo List")
        self.audio_manager = AudioManager()

        # UI组件
        self.current_theme = 'light'
        self.is_floating_mode = False
        self.floating_window = None
        
        # 初始化UI
        self.init_ui()
        self.setup_events()
        self.setup_notifications()
        self.setup_system_integration()

        # 居中显示
        self.Center()
    
    def init_ui(self) -> None:
        """初始化用户界面"""
        # 设置窗口图标
        # self.SetIcon(wx.Icon("assets/icons/app_icon.ico", wx.BITMAP_TYPE_ICO))
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 创建主布局
        self.create_layout()
        
        # 应用样式
        self.apply_theme()
    
    def create_menu_bar(self) -> None:
        """创建菜单栏"""
        menubar = wx.MenuBar()
        
        # 文件菜单
        file_menu = wx.Menu()
        file_menu.Append(wx.ID_NEW, "新建任务\tCtrl+N")
        file_menu.AppendSeparator()
        file_menu.Append(wx.ID_EXIT, "退出\tCtrl+Q")
        
        # 编辑菜单
        edit_menu = wx.Menu()
        edit_menu.Append(wx.ID_PREFERENCES, "设置\tCtrl+,")
        
        # 视图菜单
        view_menu = wx.Menu()
        self.floating_menu_item = view_menu.AppendCheckItem(wx.ID_ANY, "悬浮模式\tF11")
        view_menu.AppendSeparator()
        theme_submenu = wx.Menu()
        theme_submenu.AppendRadioItem(wx.ID_ANY, "明亮主题")
        theme_submenu.AppendRadioItem(wx.ID_ANY, "暗黑主题")
        view_menu.AppendSubMenu(theme_submenu, "主题")
        
        # 帮助菜单
        help_menu = wx.Menu()
        help_menu.Append(wx.ID_ABOUT, "关于")
        
        # 添加到菜单栏
        menubar.Append(file_menu, "文件")
        menubar.Append(edit_menu, "编辑")
        menubar.Append(view_menu, "视图")
        menubar.Append(help_menu, "帮助")
        
        self.SetMenuBar(menubar)
    
    def create_toolbar(self) -> None:
        """创建工具栏"""
        toolbar = self.CreateToolBar(wx.TB_HORIZONTAL | wx.TB_FLAT)
        
        # 添加工具按钮
        toolbar.AddTool(wx.ID_NEW, "新建", wx.ArtProvider.GetBitmap(wx.ART_NEW), "新建任务")
        toolbar.AddSeparator()
        # 使用兼容的图标
        refresh_bitmap = wx.ArtProvider.GetBitmap(wx.ART_REDO) if hasattr(wx, 'ART_REFRESH') else wx.ArtProvider.GetBitmap(wx.ART_REDO)
        toolbar.AddTool(wx.ID_REFRESH, "刷新", refresh_bitmap, "刷新列表")
        toolbar.AddSeparator()
        
        # 搜索框
        search_label = wx.StaticText(toolbar, label="搜索:")
        toolbar.AddControl(search_label)
        self.search_ctrl = wx.SearchCtrl(toolbar, size=(200, -1))
        self.search_ctrl.ShowCancelButton(True)
        toolbar.AddControl(self.search_ctrl)
        
        toolbar.Realize()
    
    def create_status_bar(self) -> None:
        """创建状态栏"""
        self.status_bar = self.CreateStatusBar(3)
        self.status_bar.SetStatusWidths([-1, 150, 200])
        
        # 启动时间更新定时器
        self.timer = wx.Timer(self)
        self.timer.Start(1000)  # 每秒更新一次
        
    def create_layout(self) -> None:
        """创建主布局"""
        # 创建主面板
        self.main_panel = wx.Panel(self)
        self.main_panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.current_theme))
        
        # 创建水平分割器
        self.splitter = wx.SplitterWindow(self.main_panel, style=wx.SP_3D | wx.SP_LIVE_UPDATE)
        
        # 创建左侧面板（侧边栏）
        self.sidebar_panel = wx.Panel(self.splitter)
        self.sidebar_panel.SetBackgroundColour(UIStyles.get_color('bg_secondary', self.current_theme))
        self.sidebar_panel.SetMinSize((250, -1))
        
        # 创建右侧面板（任务列表）
        self.content_panel = wx.Panel(self.splitter)
        self.content_panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.current_theme))
        
        # 设置分割器
        self.splitter.SplitVertically(self.sidebar_panel, self.content_panel, 250)
        self.splitter.SetMinimumPaneSize(200)
        
        # 主布局
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        main_sizer.Add(self.splitter, 1, wx.EXPAND)
        self.main_panel.SetSizer(main_sizer)
        
        # 创建侧边栏内容
        self.create_sidebar()
        
        # 创建内容区域
        self.create_content_area()
    
    def create_sidebar(self) -> None:
        """创建侧边栏"""
        sidebar_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 分类标题
        category_label = wx.StaticText(self.sidebar_panel, label="分类")
        category_label.SetFont(UIStyles.get_font('medium', bold=True))
        sidebar_sizer.Add(category_label, 0, wx.ALL, UIStyles.SPACING['md'])
        
        # 分类列表
        self.category_list = wx.ListCtrl(self.sidebar_panel, style=wx.LC_REPORT | wx.LC_SINGLE_SEL | wx.LC_NO_HEADER)
        self.category_list.AppendColumn("分类", width=200)
        sidebar_sizer.Add(self.category_list, 1, wx.EXPAND | wx.LEFT | wx.RIGHT, UIStyles.SPACING['md'])

        # 加载分类数据
        self.refresh_categories()

        # 创建默认分类（仅在首次运行时）
        self.ensure_default_categories()
        
        # 文件夹标题
        folder_label = wx.StaticText(self.sidebar_panel, label="文件夹")
        folder_label.SetFont(UIStyles.get_font('medium', bold=True))
        sidebar_sizer.Add(folder_label, 0, wx.ALL, UIStyles.SPACING['md'])
        
        # 文件夹树（支持多选）
        self.folder_tree = wx.TreeCtrl(self.sidebar_panel, style=wx.TR_DEFAULT_STYLE | wx.TR_MULTIPLE)
        sidebar_sizer.Add(self.folder_tree, 1, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, UIStyles.SPACING['md'])

        # 多选状态跟踪
        self.selected_folders = set()  # 存储选中的文件夹ID

        # 加载文件夹数据
        self.refresh_folders()

        # 创建默认文件夹（仅在首次运行时）
        self.ensure_default_folders()

        self.sidebar_panel.SetSizer(sidebar_sizer)
    
    def create_content_area(self) -> None:
        """创建内容区域"""
        content_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 任务列表标题栏
        title_panel = wx.Panel(self.content_panel)
        title_panel.SetBackgroundColour(UIStyles.get_color('bg_secondary', self.current_theme))
        title_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        self.list_title = wx.StaticText(title_panel, label="所有任务")
        self.list_title.SetFont(UIStyles.get_font('large', bold=True))
        title_sizer.Add(self.list_title, 1, wx.ALIGN_CENTER_VERTICAL | wx.ALL, UIStyles.SPACING['md'])
        
        # 排序选择
        sort_label = wx.StaticText(title_panel, label="排序:")
        title_sizer.Add(sort_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, UIStyles.SPACING['sm'])
        
        self.sort_choice = wx.Choice(title_panel, choices=["优先级", "创建时间", "到期时间"])
        self.sort_choice.SetSelection(0)
        title_sizer.Add(self.sort_choice, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, UIStyles.SPACING['md'])
        
        title_panel.SetSizer(title_sizer)
        content_sizer.Add(title_panel, 0, wx.EXPAND)
        
        # 任务列表滚动区域
        self.scroll_panel = wx.ScrolledWindow(self.content_panel)
        self.scroll_panel.SetScrollRate(0, 20)
        self.scroll_panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.current_theme))
        
        # 任务列表容器
        self.todo_list_sizer = wx.BoxSizer(wx.VERTICAL)
        self.scroll_panel.SetSizer(self.todo_list_sizer)
        
        content_sizer.Add(self.scroll_panel, 1, wx.EXPAND)
        self.content_panel.SetSizer(content_sizer)
        
        # 加载任务列表
        self.refresh_todo_list()
    
    def apply_theme(self) -> None:
        """应用主题"""
        # 更新UIStyles的当前主题
        UIStyles.set_theme(self.current_theme)

        # 设置窗口背景色
        self.SetBackgroundColour(UIStyles.get_color('bg_primary', self.current_theme))

        # 递归应用主题到所有子控件
        self._apply_theme_to_all_children(self)

        # 刷新显示
        self.Refresh()
        self.Update()

    def _apply_theme_to_all_children(self, parent: wx.Window) -> None:
        """递归应用主题到所有子控件"""
        for child in parent.GetChildren():
            try:
                if isinstance(child, wx.Panel):
                    # 区分不同类型的面板
                    if child == self.sidebar_panel:
                        child.SetBackgroundColour(UIStyles.get_color('bg_secondary', self.current_theme))
                    else:
                        child.SetBackgroundColour(UIStyles.get_color('bg_primary', self.current_theme))
                elif isinstance(child, wx.StaticText):
                    child.SetForegroundColour(UIStyles.get_color('text_primary', self.current_theme))
                elif isinstance(child, wx.Button):
                    child.SetBackgroundColour(UIStyles.get_color('bg_card', self.current_theme))
                    child.SetForegroundColour(UIStyles.get_color('text_primary', self.current_theme))
                elif isinstance(child, wx.TextCtrl):
                    child.SetBackgroundColour(UIStyles.get_color('bg_card', self.current_theme))
                    child.SetForegroundColour(UIStyles.get_color('text_primary', self.current_theme))
                elif isinstance(child, wx.ListCtrl):
                    child.SetBackgroundColour(UIStyles.get_color('bg_card', self.current_theme))
                    child.SetForegroundColour(UIStyles.get_color('text_primary', self.current_theme))
                elif isinstance(child, wx.TreeCtrl):
                    child.SetBackgroundColour(UIStyles.get_color('bg_card', self.current_theme))
                    child.SetForegroundColour(UIStyles.get_color('text_primary', self.current_theme))
                elif isinstance(child, wx.SearchCtrl):
                    child.SetBackgroundColour(UIStyles.get_color('bg_card', self.current_theme))
                    child.SetForegroundColour(UIStyles.get_color('text_primary', self.current_theme))

                # 刷新子控件
                child.Refresh()

                # 递归处理子控件
                if hasattr(child, 'GetChildren'):
                    self._apply_theme_to_all_children(child)
            except Exception as e:
                # 忽略无法设置主题的控件
                continue
    
    def setup_events(self) -> None:
        """设置事件处理"""
        # 菜单事件
        self.Bind(wx.EVT_MENU, self.on_new_todo, id=wx.ID_NEW)
        self.Bind(wx.EVT_MENU, self.on_exit, id=wx.ID_EXIT)
        self.Bind(wx.EVT_MENU, self.on_settings, id=wx.ID_PREFERENCES)
        self.Bind(wx.EVT_MENU, self.on_about, id=wx.ID_ABOUT)

        # 悬浮模式菜单事件
        self.Bind(wx.EVT_MENU, self.on_toggle_floating_mode, self.floating_menu_item)
        
        # 工具栏事件
        self.Bind(wx.EVT_TOOL, self.on_new_todo, id=wx.ID_NEW)
        self.Bind(wx.EVT_TOOL, self.on_refresh, id=wx.ID_REFRESH)
        
        # 搜索事件
        self.search_ctrl.Bind(wx.EVT_TEXT, self.on_search)

        # 侧边栏事件
        self.category_list.Bind(wx.EVT_LIST_ITEM_RIGHT_CLICK, self.on_category_right_click)
        self.category_list.Bind(wx.EVT_LIST_ITEM_SELECTED, self.on_category_select)
        self.folder_tree.Bind(wx.EVT_TREE_ITEM_RIGHT_CLICK, self.on_folder_right_click)
        self.folder_tree.Bind(wx.EVT_TREE_SEL_CHANGED, self.on_folder_select)
        self.folder_tree.Bind(wx.EVT_TREE_ITEM_ACTIVATED, self.on_folder_double_click)

        # 定时器事件
        self.Bind(wx.EVT_TIMER, self.on_timer)

        # 窗口事件
        self.Bind(wx.EVT_CLOSE, self.on_close)
    
    def setup_notifications(self) -> None:
        """设置通知系统"""
        # 启动通知管理器
        self.notification_manager.start_scheduler()

        # 设置任务提醒
        self.notification_manager.setup_todo_reminders(
            lambda: self.todo_manager.get_todos({'completed': False})
        )

    def setup_system_integration(self) -> None:
        """设置系统集成功能"""
        # 创建系统托盘图标
        self.system_integration.create_tray_icon(self)

        # 设置窗口关闭事件处理
        self.Bind(wx.EVT_ICONIZE, self.on_iconize)

    def refresh_todo_list(self) -> None:
        """刷新任务列表"""
        # 清空现有列表
        self.todo_list_sizer.Clear(True)

        # 获取任务列表
        todos = self.todo_manager.get_todos()

        # 创建任务卡片
        for todo in todos:
            # 使用新的TodoCard组件
            card = TodoCard(
                parent=self.scroll_panel,
                todo=todo,
                animation_manager=self.animation_manager,
                on_complete_callback=self.on_todo_complete,
                on_edit_callback=self.on_todo_edit,
                on_delete_callback=self.on_todo_delete,
                theme=self.current_theme
            )

            self.todo_list_sizer.Add(card, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['sm'])

        # 更新布局
        self.scroll_panel.FitInside()
        self.scroll_panel.Layout()

    def on_new_todo(self, event) -> None:
        """新建任务事件"""
        # 获取分类和文件夹列表
        categories = self.get_categories()
        folders = self.get_folders()

        # 创建添加任务对话框
        dlg = AddTodoDialog(self, categories, folders, self.current_theme)

        if dlg.ShowModal() == wx.ID_OK:
            todo_data = dlg.get_todo_data()
            # 添加任务到数据库
            todo_id = self.todo_manager.add_todo(
                title=todo_data['title'],
                description=todo_data['description'],
                priority=todo_data['priority'],
                category_id=todo_data['category_id'],
                folder_id=todo_data['folder_id'],
                due_date=todo_data['due_date']
            )

            if todo_id > 0:
                self.refresh_todo_list()
                self.update_status_bar()

                # 播放成功音效
                self.audio_manager.play_task_add_sound()

                # 显示成功消息
                wx.MessageBox("任务创建成功！", "成功", wx.OK | wx.ICON_INFORMATION)
            else:
                # 播放错误音效
                self.audio_manager.play_error_sound()
                wx.MessageBox("任务创建失败，请重试。", "错误", wx.OK | wx.ICON_ERROR)

        dlg.Destroy()

    def on_refresh(self, event) -> None:
        """刷新事件"""
        self.refresh_todo_list()
        self.update_status_bar()

    def on_search(self, event) -> None:
        """搜索事件"""
        search_text = self.search_ctrl.GetValue().strip()

        if search_text:
            # 执行搜索
            self.perform_search(search_text)
        else:
            # 清空搜索，显示所有任务
            self.refresh_todo_list()

    def perform_search(self, search_text: str) -> None:
        """执行搜索"""
        # 获取所有任务
        all_todos = self.todo_manager.get_todos()

        # 过滤匹配的任务
        filtered_todos = []
        search_lower = search_text.lower()

        for todo in all_todos:
            # 搜索标题和描述
            if (search_lower in todo.title.lower() or
                (todo.description and search_lower in todo.description.lower())):
                filtered_todos.append(todo)

        # 更新任务列表显示
        self.display_filtered_todos(filtered_todos, f"搜索结果: {search_text}")

    def display_filtered_todos(self, todos, title_text: str = "所有任务") -> None:
        """显示过滤后的任务列表"""
        # 更新标题
        self.list_title.SetLabel(title_text)

        # 清空现有列表
        self.todo_list_sizer.Clear(True)

        if not todos:
            # 显示无结果提示
            no_result_label = wx.StaticText(self.scroll_panel, label="没有找到匹配的任务")
            no_result_label.SetFont(UIStyles.get_font('medium'))
            no_result_label.SetForegroundColour(UIStyles.get_color('text_secondary', self.current_theme))
            self.todo_list_sizer.Add(no_result_label, 0, wx.ALL | wx.CENTER, UIStyles.SPACING['lg'])
        else:
            # 创建任务卡片
            for todo in todos:
                card = TodoCard(
                    parent=self.scroll_panel,
                    todo=todo,
                    animation_manager=self.animation_manager,
                    on_complete_callback=self.on_todo_complete,
                    on_edit_callback=self.on_todo_edit,
                    on_delete_callback=self.on_todo_delete,
                    theme=self.current_theme
                )

                self.todo_list_sizer.Add(card, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['sm'])

        # 更新布局
        self.scroll_panel.FitInside()
        self.scroll_panel.Layout()

    def on_settings(self, event) -> None:
        """设置事件"""
        # 获取当前设置
        current_settings = self.get_current_settings()

        # 创建设置对话框
        dlg = SettingsDialog(self, current_settings, self.current_theme)

        result = dlg.ShowModal()
        if result == wx.ID_OK:
            # 应用新设置
            new_settings = dlg.get_settings_data()
            self.apply_settings(new_settings)
            wx.MessageBox("设置已保存并应用！", "设置", wx.OK | wx.ICON_INFORMATION)

        dlg.Destroy()

    def on_about(self, event) -> None:
        """关于事件"""
        info = wx.adv.AboutDialogInfo()
        info.SetName("Todo List")
        info.SetVersion("1.0.0")
        info.SetDescription("现代化的任务管理应用")
        info.SetCopyright("(C) 2024")
        wx.adv.AboutBox(info)

    def on_timer(self, event) -> None:
        """定时器事件"""
        self.update_status_bar()

    def on_exit(self, event) -> None:
        """退出事件"""
        self.Close()

    def on_iconize(self, event) -> None:
        """窗口最小化事件"""
        if event.IsIconized():
            # 最小化到托盘
            self.system_integration.minimize_to_tray()
        event.Skip()

    def on_close(self, event) -> None:
        """关闭事件"""
        # 询问是否最小化到托盘
        if not self.system_integration.is_minimized_to_tray:
            dlg = wx.MessageDialog(self, "是否最小化到系统托盘？\n选择'否'将完全退出程序。",
                                  "确认", wx.YES_NO | wx.CANCEL | wx.ICON_QUESTION)
            dlg.SetYesNoLabels("最小化到托盘", "完全退出")

            result = dlg.ShowModal()
            dlg.Destroy()

            if result == wx.ID_YES:
                # 最小化到托盘
                self.system_integration.minimize_to_tray()
                return
            elif result == wx.ID_CANCEL:
                # 取消关闭
                return

        # 完全退出程序
        self._cleanup_and_exit()

    def _cleanup_and_exit(self) -> None:
        """清理资源并退出"""
        # 停止定时器和通知
        if hasattr(self, 'timer'):
            self.timer.Stop()

        if hasattr(self, 'notification_manager'):
            self.notification_manager.stop_scheduler()

        # 清理系统集成
        if hasattr(self, 'system_integration'):
            self.system_integration.cleanup()

        # 关闭数据库连接
        if hasattr(self, 'db_manager'):
            self.db_manager.close()

        # 销毁窗口
        self.Destroy()

    def update_status_bar(self) -> None:
        """更新状态栏"""
        # 任务统计
        todos = self.todo_manager.get_todos()
        completed_count = len([t for t in todos if t.completed])
        total_count = len(todos)

        self.status_bar.SetStatusText(f"任务: {completed_count}/{total_count}", 0)
        self.status_bar.SetStatusText("就绪", 1)
        self.status_bar.SetStatusText(datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 2)

    def toggle_floating_mode(self) -> None:
        """切换悬浮模式"""
        self.is_floating_mode = not self.is_floating_mode

        if self.is_floating_mode:
            # 创建悬浮窗
            try:
                if not self.floating_window:
                    self.floating_window = FloatingWindow(self, self.todo_manager, self.current_theme)

                self.floating_window.Show()
                self.floating_window.Raise()

                # 隐藏主窗口
                self.Hide()

                print("悬浮窗模式已启用")
            except Exception as e:
                print(f"启用悬浮窗模式失败: {e}")
                self.is_floating_mode = False
                wx.MessageBox(f"启用悬浮窗模式失败: {e}", "错误", wx.OK | wx.ICON_ERROR)
        else:
            # 关闭悬浮窗
            try:
                if self.floating_window:
                    self.floating_window.Close()
                    self.floating_window = None

                # 显示主窗口
                self.Show()
                self.Raise()

                print("悬浮窗模式已关闭")
            except Exception as e:
                print(f"关闭悬浮窗模式失败: {e}")

        # 更新菜单状态
        if hasattr(self, 'floating_menu_item'):
            self.floating_menu_item.Check(self.is_floating_mode)

    def on_floating_window_closed(self) -> None:
        """悬浮窗关闭回调"""
        self.is_floating_mode = False
        self.floating_window = None

        # 显示主窗口
        self.Show()
        self.Raise()

        # 更新菜单状态
        if hasattr(self, 'floating_menu_item'):
            self.floating_menu_item.Check(False)

    def on_toggle_floating_mode(self, event) -> None:
        """切换悬浮模式菜单事件"""
        self.toggle_floating_mode()

    def get_categories(self):
        """获取分类列表"""
        try:
            return self.category_manager.get_categories()
        except Exception as e:
            print(f"获取分类列表失败: {e}")
            return []

    def get_folders(self):
        """获取文件夹列表"""
        try:
            return self.folder_manager.get_folders()
        except Exception as e:
            print(f"获取文件夹列表失败: {e}")
            return []

    def get_current_settings(self):
        """获取当前设置"""
        # TODO: 从数据库或配置文件获取设置
        return {
            'theme': self.current_theme,
            'auto_start': False,
            'minimize_to_tray': False,
            'remember_window': True,
            'always_on_top': False,
            'auto_backup': True,
            'font_size': 'normal',
            'enable_animations': True,
            'animation_speed': 50,
            'notifications_enabled': True,
            'sound_enabled': True,
            'reminder_time': 15,
            'daily_summary': True,
            'summary_time': '09:00',
            'global_hotkey': False,
            'hotkey': 'Ctrl+Alt+T',
            'hardware_acceleration': True,
            'reduce_animations': False,
            'debug_mode': False,
            'log_level': 'info'
        }

    def apply_settings(self, settings):
        """应用设置"""
        # 应用主题设置
        if settings.get('theme') != self.current_theme:
            old_theme = self.current_theme
            self.current_theme = settings['theme']
            print(f"主题切换: {old_theme} -> {self.current_theme}")

            # 立即应用主题
            self.apply_theme()

            # 刷新任务列表以应用新主题
            self.refresh_todo_list()

            # 刷新侧边栏
            self.refresh_categories()
            self.refresh_folders()

        # 应用开机自启设置
        if 'auto_start' in settings:
            self.system_integration.setup_autostart(settings['auto_start'])

        # 应用窗口设置
        if settings.get('always_on_top'):
            self.SetWindowStyle(self.GetWindowStyle() | wx.STAY_ON_TOP)
        else:
            self.SetWindowStyle(self.GetWindowStyle() & ~wx.STAY_ON_TOP)

        # 应用音效设置
        if hasattr(self, 'audio_manager'):
            self.audio_manager.set_enabled(settings.get('sound_enabled', True))

        # 应用通知设置
        if hasattr(self, 'notification_manager'):
            self.notification_manager.set_sound_enabled(settings.get('sound_enabled', True))

        # TODO: 保存设置到数据库或配置文件
        print(f"应用设置: {settings}")

    def refresh_categories(self) -> None:
        """刷新分类列表"""
        self.category_list.DeleteAllItems()

        try:
            categories = self.category_manager.get_categories()
            for i, category in enumerate(categories):
                display_text = f"{category.icon} {category.name}" if category.icon else category.name
                index = self.category_list.InsertItem(i, display_text)
                self.category_list.SetItemData(index, category.id)
        except Exception as e:
            print(f"刷新分类列表失败: {e}")

    def refresh_folders(self) -> None:
        """刷新文件夹树"""
        self.folder_tree.DeleteAllItems()

        try:
            # 添加根节点
            root = self.folder_tree.AddRoot("文件夹")

            # 获取文件夹树结构
            folder_tree = self.folder_manager.get_folder_tree()
            self._add_folder_nodes(root, folder_tree)

            # 展开根节点
            self.folder_tree.Expand(root)
        except Exception as e:
            print(f"刷新文件夹树失败: {e}")

    def _add_folder_nodes(self, parent_node, folder_tree) -> None:
        """递归添加文件夹节点"""
        for folder_data in folder_tree:
            folder = folder_data['folder']
            task_count = folder_data['task_count']

            # 创建显示文本
            display_text = f"{folder.name} ({task_count})"

            # 添加节点
            node = self.folder_tree.AppendItem(parent_node, display_text)
            self.folder_tree.SetItemData(node, folder.id)

            # 递归添加子文件夹
            if folder_data['children']:
                self._add_folder_nodes(node, folder_data['children'])

    def ensure_default_folders(self) -> None:
        """确保默认文件夹存在（避免重复创建）"""
        try:
            # 检查是否已有文件夹，如果没有则创建默认文件夹
            existing_folders = self.folder_manager.get_root_folders()
            if len(existing_folders) == 0:
                print("未发现现有文件夹，创建默认文件夹...")
                created_count = self.folder_manager.create_default_folders()
                if created_count > 0:
                    print(f"成功创建 {created_count} 个默认文件夹")
                    # 刷新文件夹显示
                    self.refresh_folders()
            else:
                print(f"发现 {len(existing_folders)} 个现有文件夹，跳过默认文件夹创建")
        except Exception as e:
            print(f"确保默认文件夹存在时出错: {e}")

    def ensure_default_categories(self) -> None:
        """确保默认分类存在（避免重复创建）"""
        try:
            # 检查是否已有分类，如果没有则创建默认分类
            existing_categories = self.category_manager.get_categories()
            if len(existing_categories) == 0:
                print("未发现现有分类，创建默认分类...")
                self.category_manager.create_default_categories()
                print("默认分类创建完成")
                # 刷新分类显示
                self.refresh_categories()
            else:
                print(f"发现 {len(existing_categories)} 个现有分类，跳过默认分类创建")
        except Exception as e:
            print(f"确保默认分类存在时出错: {e}")

    def on_category_right_click(self, event) -> None:
        """分类右键菜单"""
        # 创建右键菜单
        menu = wx.Menu()

        # 添加菜单项
        add_item = menu.Append(wx.ID_ANY, "新建分类")
        manage_item = menu.Append(wx.ID_ANY, "管理分类")

        # 如果选中了分类，添加删除选项
        selected = self.category_list.GetFirstSelected()
        if selected != -1:
            menu.AppendSeparator()
            delete_item = menu.Append(wx.ID_ANY, "删除分类")
            self.Bind(wx.EVT_MENU, lambda evt: self.on_delete_category(selected), delete_item)

        # 绑定事件
        self.Bind(wx.EVT_MENU, self.on_add_category, add_item)
        self.Bind(wx.EVT_MENU, self.on_manage_categories, manage_item)

        # 显示菜单
        self.PopupMenu(menu)
        menu.Destroy()

    def on_folder_right_click(self, event) -> None:
        """文件夹右键菜单"""
        # 创建右键菜单
        menu = wx.Menu()

        # 添加菜单项
        add_item = menu.Append(wx.ID_ANY, "新建文件夹")

        # 多选相关菜单
        menu.AppendSeparator()
        select_all_item = menu.Append(wx.ID_ANY, "全选文件夹")
        deselect_all_item = menu.Append(wx.ID_ANY, "取消全选")

        # 获取选中的文件夹
        selected_items = self.folder_tree.GetSelections()

        # 如果选中了文件夹，添加删除选项
        selected_item = event.GetItem()
        if selected_item.IsOk() and selected_item != self.folder_tree.GetRootItem():
            menu.AppendSeparator()
            if len(selected_items) > 1:
                delete_item = menu.Append(wx.ID_ANY, f"批量删除文件夹 ({len(selected_items)}个)")
                self.Bind(wx.EVT_MENU, self.on_batch_delete_folders, delete_item)
            else:
                delete_item = menu.Append(wx.ID_ANY, "删除文件夹")
                self.Bind(wx.EVT_MENU, lambda evt: self.on_delete_folder(selected_item), delete_item)

        # 绑定事件
        self.Bind(wx.EVT_MENU, lambda evt: self.on_add_folder(selected_item), add_item)
        self.Bind(wx.EVT_MENU, self.on_select_all_folders, select_all_item)
        self.Bind(wx.EVT_MENU, self.on_deselect_all_folders, deselect_all_item)

        # 显示菜单
        self.PopupMenu(menu)
        menu.Destroy()

    def on_category_select(self, event) -> None:
        """分类选择事件"""
        selected = event.GetIndex()
        if selected != -1:
            category_id = self.category_list.GetItemData(selected)
            # 过滤显示该分类的任务
            self.filter_todos_by_category(category_id)

    def on_folder_select(self, event) -> None:
        """文件夹选择事件（支持多选）"""
        # 获取所有选中的文件夹
        selected_items = self.folder_tree.GetSelections()
        selected_folder_ids = []

        for item in selected_items:
            if item.IsOk() and item != self.folder_tree.GetRootItem():
                folder_id = self.folder_tree.GetItemData(item)
                if folder_id:
                    selected_folder_ids.append(folder_id)

        # 更新选中状态
        self.selected_folders = set(selected_folder_ids)

        # 过滤显示选中文件夹的任务
        if selected_folder_ids:
            self.filter_todos_by_folders(selected_folder_ids)
        else:
            # 如果没有选中文件夹，显示所有任务
            self.refresh_todo_list()

    def on_folder_double_click(self, event) -> None:
        """文件夹双击事件"""
        selected_item = event.GetItem()
        if selected_item.IsOk() and selected_item != self.folder_tree.GetRootItem():
            # 双击时切换展开/折叠状态
            if self.folder_tree.IsExpanded(selected_item):
                self.folder_tree.Collapse(selected_item)
            else:
                self.folder_tree.Expand(selected_item)

    def on_add_category(self, event) -> None:
        """添加分类事件"""
        dlg = wx.TextEntryDialog(self, "请输入分类名称:", "新建分类")
        if dlg.ShowModal() == wx.ID_OK:
            name = dlg.GetValue().strip()
            if name:
                try:
                    category_id = self.category_manager.add_category(name)
                    if category_id > 0:
                        self.refresh_categories()
                        self.audio_manager.play_success_sound()
                        wx.MessageBox("分类创建成功！", "成功", wx.OK | wx.ICON_INFORMATION)
                    else:
                        self.audio_manager.play_error_sound()
                        wx.MessageBox("分类创建失败，请重试。", "错误", wx.OK | wx.ICON_ERROR)
                except ValueError as e:
                    self.audio_manager.play_error_sound()
                    wx.MessageBox(str(e), "错误", wx.OK | wx.ICON_ERROR)
        dlg.Destroy()

    def on_delete_category(self, selected_index: int) -> None:
        """删除分类事件"""
        if selected_index == -1:
            return

        category_id = self.category_list.GetItemData(selected_index)
        category = self.category_manager.get_category_by_id(category_id)

        if category:
            dlg = wx.MessageDialog(
                self,
                f"确定要删除分类 '{category.name}' 吗？\n删除后该分类下的任务将变为无分类。",
                "确认删除",
                wx.YES_NO | wx.ICON_QUESTION
            )

            if dlg.ShowModal() == wx.ID_YES:
                try:
                    if self.category_manager.delete_category(category_id):
                        self.refresh_categories()
                        self.refresh_todo_list()  # 刷新任务列表
                        self.audio_manager.play_task_delete_sound()
                        wx.MessageBox("分类删除成功！", "成功", wx.OK | wx.ICON_INFORMATION)
                    else:
                        self.audio_manager.play_error_sound()
                        wx.MessageBox("分类删除失败，请重试。", "错误", wx.OK | wx.ICON_ERROR)
                except ValueError as e:
                    self.audio_manager.play_error_sound()
                    wx.MessageBox(str(e), "错误", wx.OK | wx.ICON_ERROR)

            dlg.Destroy()

    def on_manage_categories(self, event) -> None:
        """管理分类事件"""
        try:
            categories = self.category_manager.get_categories()
            dlg = CategoryManagerDialog(self, categories, self.current_theme)

            if dlg.ShowModal() == wx.ID_OK:
                success_count = 0
                error_count = 0

                # 处理新增的分类
                new_categories = dlg.get_new_categories()
                for category in new_categories:
                    try:
                        new_id = self.category_manager.add_category(
                            category.name, category.color, category.icon
                        )
                        if new_id > 0:
                            success_count += 1
                            print(f"成功添加新分类: {category.name} (ID: {new_id})")
                        else:
                            error_count += 1
                            print(f"添加新分类失败: {category.name}")
                    except Exception as e:
                        error_count += 1
                        print(f"添加新分类异常: {category.name} - {e}")

                # 处理修改的分类
                modified_categories = dlg.get_modified_categories()
                for category in modified_categories:
                    try:
                        success = self.category_manager.update_category(
                            category.id, category.name, category.color, category.icon
                        )
                        if success:
                            success_count += 1
                            print(f"成功更新分类: {category.name}")
                        else:
                            error_count += 1
                            print(f"更新分类失败: {category.name}")
                    except Exception as e:
                        error_count += 1
                        print(f"更新分类异常: {category.name} - {e}")

                # 处理删除的分类
                deleted_categories = dlg.get_deleted_categories()
                for category in deleted_categories:
                    try:
                        success = self.category_manager.delete_category(category.id)
                        if success:
                            success_count += 1
                            print(f"成功删除分类: {category.name}")
                        else:
                            error_count += 1
                            print(f"删除分类失败: {category.name}")
                    except Exception as e:
                        error_count += 1
                        print(f"删除分类异常: {category.name} - {e}")

                # 刷新显示
                self.refresh_categories()
                self.refresh_todo_list()

                # 显示结果
                total_operations = len(new_categories) + len(modified_categories) + len(deleted_categories)
                if total_operations > 0:
                    if error_count == 0:
                        self.audio_manager.play_success_sound()
                        wx.MessageBox(f"分类管理操作完成！成功处理 {success_count} 项操作。", "成功", wx.OK | wx.ICON_INFORMATION)
                    else:
                        self.audio_manager.play_error_sound()
                        wx.MessageBox(f"分类管理操作完成！成功 {success_count} 项，失败 {error_count} 项。", "部分成功", wx.OK | wx.ICON_WARNING)

            dlg.Destroy()
        except Exception as e:
            self.audio_manager.play_error_sound()
            wx.MessageBox(f"分类管理失败: {str(e)}", "错误", wx.OK | wx.ICON_ERROR)

    def on_add_folder(self, parent_item) -> None:
        """添加文件夹事件"""
        dlg = wx.TextEntryDialog(self, "请输入文件夹名称:", "新建文件夹")
        if dlg.ShowModal() == wx.ID_OK:
            name = dlg.GetValue().strip()
            if name:
                try:
                    # 确定父文件夹ID
                    parent_id = None
                    if parent_item.IsOk() and parent_item != self.folder_tree.GetRootItem():
                        parent_id = self.folder_tree.GetItemData(parent_item)

                    folder_id = self.folder_manager.add_folder(name, parent_id)
                    if folder_id > 0:
                        self.refresh_folders()
                        self.audio_manager.play_success_sound()
                        wx.MessageBox("文件夹创建成功！", "成功", wx.OK | wx.ICON_INFORMATION)
                    else:
                        self.audio_manager.play_error_sound()
                        wx.MessageBox("文件夹创建失败，请重试。", "错误", wx.OK | wx.ICON_ERROR)
                except ValueError as e:
                    self.audio_manager.play_error_sound()
                    wx.MessageBox(str(e), "错误", wx.OK | wx.ICON_ERROR)
        dlg.Destroy()

    def on_delete_folder(self, folder_item) -> None:
        """删除文件夹事件"""
        if not folder_item.IsOk() or folder_item == self.folder_tree.GetRootItem():
            return

        folder_id = self.folder_tree.GetItemData(folder_item)
        folder = self.folder_manager.get_folder_by_id(folder_id)

        if folder:
            dlg = wx.MessageDialog(
                self,
                f"确定要删除文件夹 '{folder.name}' 吗？\n删除后该文件夹下的任务将变为无文件夹，子文件夹将移动到根目录。",
                "确认删除",
                wx.YES_NO | wx.ICON_QUESTION
            )

            if dlg.ShowModal() == wx.ID_YES:
                try:
                    if self.folder_manager.delete_folder(folder_id):
                        self.refresh_folders()
                        self.refresh_todo_list()  # 刷新任务列表
                        self.audio_manager.play_task_delete_sound()
                        wx.MessageBox("文件夹删除成功！", "成功", wx.OK | wx.ICON_INFORMATION)
                    else:
                        self.audio_manager.play_error_sound()
                        wx.MessageBox("文件夹删除失败，请重试。", "错误", wx.OK | wx.ICON_ERROR)
                except ValueError as e:
                    self.audio_manager.play_error_sound()
                    wx.MessageBox(str(e), "错误", wx.OK | wx.ICON_ERROR)

            dlg.Destroy()

    def filter_todos_by_category(self, category_id: int) -> None:
        """按分类过滤任务"""
        try:
            category = self.category_manager.get_category_by_id(category_id)
            if category:
                filtered_todos = self.todo_manager.get_todos({'category_id': category_id})
                self.display_filtered_todos(filtered_todos, f"分类: {category.name}")
        except Exception as e:
            print(f"按分类过滤任务失败: {e}")

    def filter_todos_by_folder(self, folder_id: int) -> None:
        """按文件夹过滤任务"""
        try:
            folder = self.folder_manager.get_folder_by_id(folder_id)
            if folder:
                filtered_todos = self.todo_manager.get_todos({'folder_id': folder_id})
                folder_path = self.folder_manager.get_folder_path(folder_id)
                self.display_filtered_todos(filtered_todos, f"文件夹: {folder_path}")
        except Exception as e:
            print(f"按文件夹过滤任务失败: {e}")

    def on_select_all_folders(self, event) -> None:
        """全选文件夹事件"""
        try:
            # 获取根节点
            root = self.folder_tree.GetRootItem()
            if root.IsOk():
                # 递归选择所有文件夹节点
                self._select_all_folder_children(root)

                # 触发选择事件更新任务显示
                self.on_folder_select(None)

                print("已全选所有文件夹")
        except Exception as e:
            print(f"全选文件夹失败: {e}")

    def _select_all_folder_children(self, parent_item) -> None:
        """递归选择所有子文件夹"""
        child, cookie = self.folder_tree.GetFirstChild(parent_item)
        while child.IsOk():
            # 选择当前子节点
            self.folder_tree.SelectItem(child, True)

            # 递归选择子节点的子节点
            if self.folder_tree.ItemHasChildren(child):
                self._select_all_folder_children(child)

            # 获取下一个兄弟节点
            child, cookie = self.folder_tree.GetNextChild(parent_item, cookie)

    def on_deselect_all_folders(self, event) -> None:
        """取消全选文件夹事件"""
        try:
            # 取消所有选择
            self.folder_tree.UnselectAll()

            # 清空选中状态
            self.selected_folders.clear()

            # 刷新任务显示（显示所有任务）
            self.refresh_todo_list()

            print("已取消选择所有文件夹")
        except Exception as e:
            print(f"取消全选文件夹失败: {e}")

    def on_batch_delete_folders(self, event) -> None:
        """批量删除文件夹事件"""
        try:
            selected_items = self.folder_tree.GetSelections()
            if not selected_items:
                return

            # 过滤掉根节点
            valid_items = [item for item in selected_items
                          if item.IsOk() and item != self.folder_tree.GetRootItem()]

            if not valid_items:
                return

            # 获取文件夹信息
            folder_names = []
            folder_ids = []
            for item in valid_items:
                folder_id = self.folder_tree.GetItemData(item)
                if folder_id:
                    folder = self.folder_manager.get_folder_by_id(folder_id)
                    if folder:
                        folder_names.append(folder.name)
                        folder_ids.append(folder_id)

            if not folder_ids:
                return

            # 确认删除
            folder_list = "\n".join([f"• {name}" for name in folder_names])
            dlg = wx.MessageDialog(
                self,
                f"确定要删除以下 {len(folder_names)} 个文件夹吗？\n\n{folder_list}\n\n删除后这些文件夹下的任务将变为无文件夹。",
                "确认批量删除",
                wx.YES_NO | wx.ICON_QUESTION
            )

            if dlg.ShowModal() == wx.ID_YES:
                success_count = 0
                error_count = 0

                for folder_id in folder_ids:
                    try:
                        if self.folder_manager.delete_folder(folder_id):
                            success_count += 1
                        else:
                            error_count += 1
                    except Exception as e:
                        error_count += 1
                        print(f"删除文件夹失败 (ID: {folder_id}): {e}")

                # 刷新显示
                self.refresh_folders()
                self.refresh_todo_list()

                # 显示结果
                if error_count == 0:
                    self.audio_manager.play_task_delete_sound()
                    wx.MessageBox(f"成功删除 {success_count} 个文件夹！", "删除成功", wx.OK | wx.ICON_INFORMATION)
                else:
                    self.audio_manager.play_error_sound()
                    wx.MessageBox(f"删除完成！成功 {success_count} 个，失败 {error_count} 个。", "部分成功", wx.OK | wx.ICON_WARNING)

            dlg.Destroy()

        except Exception as e:
            self.audio_manager.play_error_sound()
            wx.MessageBox(f"批量删除失败: {str(e)}", "错误", wx.OK | wx.ICON_ERROR)

    def filter_todos_by_folders(self, folder_ids: list) -> None:
        """按多个文件夹过滤任务"""
        try:
            if not folder_ids:
                self.refresh_todo_list()
                return

            # 获取所有选中文件夹的任务
            all_todos = []
            folder_names = []

            for folder_id in folder_ids:
                folder = self.folder_manager.get_folder_by_id(folder_id)
                if folder:
                    folder_names.append(folder.name)
                    folder_todos = self.todo_manager.get_todos({'folder_id': folder_id})
                    all_todos.extend(folder_todos)

            # 去重（如果任务属于多个选中文件夹）
            unique_todos = []
            seen_ids = set()
            for todo in all_todos:
                if todo.id not in seen_ids:
                    unique_todos.append(todo)
                    seen_ids.add(todo.id)

            # 显示过滤结果
            filter_text = f"文件夹: {', '.join(folder_names)}"
            self.display_filtered_todos(unique_todos, filter_text)

        except Exception as e:
            print(f"按多文件夹过滤任务失败: {e}")

    def refresh_categories(self) -> None:
        """刷新分类列表"""
        self.category_list.DeleteAllItems()

        try:
            categories = self.category_manager.get_categories()
            for i, category in enumerate(categories):
                display_text = f"{category.icon} {category.name}" if category.icon else category.name
                index = self.category_list.InsertItem(i, display_text)
                self.category_list.SetItemData(index, category.id)
        except Exception as e:
            print(f"刷新分类列表失败: {e}")

    def refresh_folders(self) -> None:
        """刷新文件夹树"""
        self.folder_tree.DeleteAllItems()

        try:
            # 添加根节点
            root = self.folder_tree.AddRoot("文件夹")

            # 获取文件夹树结构
            folder_tree = self.folder_manager.get_folder_tree()
            self._add_folder_nodes(root, folder_tree)

            # 展开根节点
            self.folder_tree.Expand(root)
        except Exception as e:
            print(f"刷新文件夹树失败: {e}")

    def _add_folder_nodes(self, parent_node, folder_tree) -> None:
        """递归添加文件夹节点"""
        for folder_data in folder_tree:
            folder = folder_data['folder']
            task_count = folder_data['task_count']

            # 创建显示文本
            display_text = f"{folder.name} ({task_count})"

            # 添加节点
            node = self.folder_tree.AppendItem(parent_node, display_text)
            self.folder_tree.SetItemData(node, folder.id)

            # 递归添加子文件夹
            if folder_data['children']:
                self._add_folder_nodes(node, folder_data['children'])

    def on_category_right_click(self, event) -> None:
        """分类右键菜单"""
        # 创建右键菜单
        menu = wx.Menu()

        # 添加菜单项
        add_item = menu.Append(wx.ID_ANY, "新建分类")
        manage_item = menu.Append(wx.ID_ANY, "管理分类")

        # 如果选中了分类，添加删除选项
        selected = self.category_list.GetFirstSelected()
        if selected != -1:
            menu.AppendSeparator()
            delete_item = menu.Append(wx.ID_ANY, "删除分类")
            self.Bind(wx.EVT_MENU, lambda evt: self.on_delete_category(selected), delete_item)

        # 绑定事件
        self.Bind(wx.EVT_MENU, self.on_add_category, add_item)
        self.Bind(wx.EVT_MENU, self.on_manage_categories, manage_item)

        # 显示菜单
        self.PopupMenu(menu)
        menu.Destroy()

    def on_folder_right_click(self, event) -> None:
        """文件夹右键菜单"""
        # 创建右键菜单
        menu = wx.Menu()

        # 添加菜单项
        add_item = menu.Append(wx.ID_ANY, "新建文件夹")

        # 如果选中了文件夹，添加删除选项
        selected_item = event.GetItem()
        if selected_item.IsOk() and selected_item != self.folder_tree.GetRootItem():
            menu.AppendSeparator()
            delete_item = menu.Append(wx.ID_ANY, "删除文件夹")
            self.Bind(wx.EVT_MENU, lambda evt: self.on_delete_folder(selected_item), delete_item)

        # 绑定事件
        self.Bind(wx.EVT_MENU, lambda evt: self.on_add_folder(selected_item), add_item)

        # 显示菜单
        self.PopupMenu(menu)
        menu.Destroy()

    def on_category_select(self, event) -> None:
        """分类选择事件"""
        selected = event.GetIndex()
        if selected != -1:
            category_id = self.category_list.GetItemData(selected)
            # 过滤显示该分类的任务
            self.filter_todos_by_category(category_id)

    def on_folder_select(self, event) -> None:
        """文件夹选择事件"""
        selected_item = event.GetItem()
        if selected_item.IsOk() and selected_item != self.folder_tree.GetRootItem():
            folder_id = self.folder_tree.GetItemData(selected_item)
            # 过滤显示该文件夹的任务
            self.filter_todos_by_folder(folder_id)

    def on_todo_complete(self, todo_id: int, completed: bool) -> None:
        """任务完成状态回调"""
        self.todo_manager.mark_completed(todo_id, completed)

        # 播放完成音效
        if completed:
            self.audio_manager.play_task_complete_sound()

        self.update_status_bar()

    def on_todo_edit(self, todo) -> None:
        """任务编辑回调"""
        # 获取分类和文件夹列表
        categories = self.get_categories()
        folders = self.get_folders()

        # 创建编辑任务对话框
        dlg = EditTodoDialog(self, todo, categories, folders, self.current_theme)

        result = dlg.ShowModal()
        if result == wx.ID_OK:
            # 保存修改
            todo_data = dlg.get_todo_data()
            success = self.todo_manager.update_todo(
                todo_id=dlg.get_todo_id(),
                title=todo_data['title'],
                description=todo_data['description'],
                priority=todo_data['priority'],
                category_id=todo_data['category_id'],
                folder_id=todo_data['folder_id'],
                due_date=todo_data['due_date']
            )

            if success:
                self.refresh_todo_list()
                self.update_status_bar()
                wx.MessageBox("任务修改成功！", "成功", wx.OK | wx.ICON_INFORMATION)
            else:
                wx.MessageBox("任务修改失败，请重试。", "错误", wx.OK | wx.ICON_ERROR)

        elif result == wx.ID_DELETE:
            # 删除任务
            self.on_todo_delete(dlg.get_todo_id())

        dlg.Destroy()

    def on_todo_delete(self, todo_id: int) -> None:
        """任务删除回调"""
        self.todo_manager.delete_todo(todo_id)

        # 播放删除音效
        self.audio_manager.play_task_delete_sound()

        self.refresh_todo_list()
        self.update_status_bar()
