-- Todo-List应用数据库结构定义

-- 分类表
CREATE TABLE IF NOT EXISTS categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    color TEXT DEFAULT '#3498db',
    icon TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 文件夹表
CREATE TABLE IF NOT EXISTS folders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    parent_id INTEGER,
    color TEXT DEFAULT '#95a5a6',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES folders(id)
);

-- 任务表
CREATE TABLE IF NOT EXISTS todos (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    description TEXT,
    priority INTEGER DEFAULT 1,
    category_id INTEGER,
    folder_id INTEGER,
    due_date DATETIME,
    completed BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id),
    FOREIGN KEY (folder_id) REFERENCES folders(id)
);

-- 设置表
CREATE TABLE IF NOT EXISTS settings (
    key TEXT PRIMARY KEY,
    value TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认分类
INSERT OR IGNORE INTO categories (name, color, icon) VALUES 
('工作', '#e74c3c', '💼'),
('个人', '#2ecc71', '👤'),
('学习', '#f39c12', '📚'),
('购物', '#9b59b6', '🛒');

-- 插入默认文件夹


-- 插入默认设置
INSERT OR IGNORE INTO settings (key, value) VALUES 
('theme', 'light'),
('window_width', '1000'),
('window_height', '700'),
('auto_start', 'false'),
('notifications_enabled', 'true'),
('sound_enabled', 'true');
