"""
编辑任务对话框
"""
import wx
import wx.adv
from datetime import datetime
from typing import Optional, Dict, Any
from ui.components.add_todo_dialog import AddTodoDialog
from core.models import Todo


class EditTodoDialog(AddTodoDialog):
    """编辑任务对话框 - 继承自AddTodoDialog"""
    
    def __init__(self, parent, todo: Todo, categories=None, folders=None, theme='light'):
        """初始化编辑对话框"""
        self.todo = todo
        
        # 调用父类初始化
        super().__init__(parent, categories, folders, theme)
        
        # 修改标题和按钮文本
        self.SetTitle("编辑任务")
        
        # 预填充数据
        self.populate_data()
        
        # 更新按钮文本
        self.update_button_text()
    
    def update_button_text(self) -> None:
        """更新按钮文本"""
        # 找到确定按钮并更新文本
        for child in self.GetChildren():
            if isinstance(child, wx.Panel):
                for panel_child in child.GetChildren():
                    if isinstance(panel_child, wx.Panel):
                        for button_child in panel_child.GetChildren():
                            if isinstance(button_child, wx.Button) and button_child.GetId() == wx.ID_OK:
                                button_child.SetLabel("保存")
                                break
    
    def populate_data(self) -> None:
        """预填充任务数据"""
        # 填充基本信息
        self.title_ctrl.SetValue(self.todo.title)
        self.desc_ctrl.SetValue(self.todo.description or "")
        
        # 设置优先级
        self.priority_choice.SetSelection(self.todo.priority - 1)  # 转换为0-based索引
        
        # 设置分类
        if self.todo.category_id and self.categories:
            for i, category in enumerate(self.categories):
                if category.id == self.todo.category_id:
                    self.category_choice.SetSelection(i + 1)  # +1因为第一个是"无"
                    break
        
        # 设置文件夹
        if self.todo.folder_id and self.folders:
            for i, folder in enumerate(self.folders):
                if folder.id == self.todo.folder_id:
                    self.folder_choice.SetSelection(i + 1)  # +1因为第一个是"无"
                    break
        
        # 设置到期时间
        if self.todo.due_date:
            self.due_checkbox.SetValue(True)
            self.date_picker.Enable(True)
            self.time_picker.Enable(True)
            
            # 设置日期
            wx_date = wx.DateTime()
            wx_date.Set(
                self.todo.due_date.day,
                self.todo.due_date.month - 1,  # wx.DateTime的月份从0开始
                self.todo.due_date.year
            )
            self.date_picker.SetValue(wx_date)
            
            # 设置时间
            wx_time = wx.DateTime()
            wx_time.SetHour(self.todo.due_date.hour)
            wx_time.SetMinute(self.todo.due_date.minute)
            self.time_picker.SetValue(wx_time)
    
    def init_ui(self) -> None:
        """初始化用户界面 - 重写以修改标题"""
        # 创建主面板
        main_panel = wx.Panel(self)
        main_panel.SetBackgroundColour(self.get_bg_color())
        
        # 创建主布局
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题区域 - 修改标题文本
        title_label = wx.StaticText(main_panel, label="编辑任务")
        title_label.SetFont(self.get_title_font())
        title_label.SetForegroundColour(self.get_text_color())
        main_sizer.Add(title_label, 0, wx.ALL | wx.CENTER, self.get_spacing('lg'))
        
        # 表单区域
        form_panel = self.create_form_panel(main_panel)
        main_sizer.Add(form_panel, 1, wx.EXPAND | wx.ALL, self.get_spacing('md'))
        
        # 按钮区域
        button_panel = self.create_edit_button_panel(main_panel)
        main_sizer.Add(button_panel, 0, wx.EXPAND | wx.ALL, self.get_spacing('md'))
        
        main_panel.SetSizer(main_sizer)
        
        # 设置对话框布局
        dialog_sizer = wx.BoxSizer(wx.VERTICAL)
        dialog_sizer.Add(main_panel, 1, wx.EXPAND)
        self.SetSizer(dialog_sizer)
    
    def create_edit_button_panel(self, parent) -> wx.Panel:
        """创建编辑模式的按钮面板"""
        button_panel = wx.Panel(parent)
        button_panel.SetBackgroundColour(self.get_bg_color())
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # 删除按钮
        delete_btn = wx.Button(button_panel, wx.ID_DELETE, "删除", size=(100, 35))
        delete_btn.SetFont(self.get_medium_font())
        delete_btn.SetForegroundColour(wx.Colour("#e74c3c"))
        button_sizer.Add(delete_btn, 0, wx.RIGHT, self.get_spacing('sm'))
        
        # 添加弹性空间
        button_sizer.AddStretchSpacer()
        
        # 取消按钮
        cancel_btn = wx.Button(button_panel, wx.ID_CANCEL, "取消", size=(100, 35))
        cancel_btn.SetFont(self.get_medium_font())
        button_sizer.Add(cancel_btn, 0, wx.RIGHT, self.get_spacing('sm'))
        
        # 保存按钮
        save_btn = wx.Button(button_panel, wx.ID_OK, "保存", size=(100, 35))
        save_btn.SetFont(self.get_medium_font(bold=True))
        save_btn.SetDefault()
        button_sizer.Add(save_btn, 0)
        
        button_panel.SetSizer(button_sizer)
        
        # 绑定删除按钮事件
        delete_btn.Bind(wx.EVT_BUTTON, self.on_delete)
        
        return button_panel
    
    def on_delete(self, event) -> None:
        """删除按钮事件"""
        dlg = wx.MessageDialog(
            self,
            f"确定要删除任务 '{self.todo.title}' 吗？\n此操作无法撤销。",
            "确认删除",
            wx.YES_NO | wx.ICON_QUESTION
        )
        
        if dlg.ShowModal() == wx.ID_YES:
            self.EndModal(wx.ID_DELETE)
        
        dlg.Destroy()
    
    def get_todo_id(self) -> int:
        """获取任务ID"""
        return self.todo.id
    
    # 辅助方法用于样式获取（避免在__init__之前调用UIStyles）
    def get_bg_color(self):
        from ui.styles import UIStyles
        return UIStyles.get_color('bg_primary', self.theme)
    
    def get_text_color(self):
        from ui.styles import UIStyles
        return UIStyles.get_color('text_primary', self.theme)
    
    def get_title_font(self):
        from ui.styles import UIStyles
        return UIStyles.get_font('large', bold=True)
    
    def get_medium_font(self, bold=False):
        from ui.styles import UIStyles
        return UIStyles.get_font('medium', bold=bold)
    
    def get_spacing(self, size):
        from ui.styles import UIStyles
        return UIStyles.SPACING[size]
