"""
音效管理器
"""
import os
import threading
from typing import Dict, Optional
import wx


class AudioManager:
    """音效管理类"""
    
    def __init__(self, sounds_dir: str = "assets/sounds"):
        """初始化音效管理器"""
        self.sounds_dir = sounds_dir
        self.enabled = True
        self.volume = 0.7  # 音量 0.0-1.0
        self.sound_cache = {}
        
        # 预定义音效文件
        self.sound_files = {
            'notification': 'notification.wav',
            'task_complete': 'task_complete.wav',
            'task_add': 'task_add.wav',
            'task_delete': 'task_delete.wav',
            'error': 'error.wav',
            'success': 'success.wav',
            'click': 'click.wav',
            'hover': 'hover.wav'
        }
        
        # 创建音效目录
        self._ensure_sounds_directory()
        
        # 生成默认音效（如果不存在）
        self._generate_default_sounds()
    
    def _ensure_sounds_directory(self) -> None:
        """确保音效目录存在"""
        if not os.path.exists(self.sounds_dir):
            os.makedirs(self.sounds_dir, exist_ok=True)
    
    def _generate_default_sounds(self) -> None:
        """生成默认音效文件"""
        # 这里可以生成简单的音效文件
        # 由于wxPython的限制，我们使用系统音效作为替代
        pass
    
    def set_enabled(self, enabled: bool) -> None:
        """设置是否启用音效"""
        self.enabled = enabled
    
    def is_enabled(self) -> bool:
        """检查音效是否启用"""
        return self.enabled
    
    def set_volume(self, volume: float) -> None:
        """设置音量 (0.0-1.0)"""
        self.volume = max(0.0, min(1.0, volume))
    
    def get_volume(self) -> float:
        """获取当前音量"""
        return self.volume
    
    def play_sound(self, sound_name: str, async_play: bool = True) -> bool:
        """播放音效"""
        if not self.enabled:
            return False
        
        try:
            if async_play:
                # 异步播放
                thread = threading.Thread(target=self._play_sound_sync, args=(sound_name,))
                thread.daemon = True
                thread.start()
                return True
            else:
                # 同步播放
                return self._play_sound_sync(sound_name)
                
        except Exception as e:
            print(f"播放音效失败: {e}")
            return False
    
    def _play_sound_sync(self, sound_name: str) -> bool:
        """同步播放音效"""
        try:
            # 首先尝试播放自定义音效文件
            if sound_name in self.sound_files:
                sound_path = os.path.join(self.sounds_dir, self.sound_files[sound_name])
                if os.path.exists(sound_path):
                    return self._play_wav_file(sound_path)
            
            # 如果自定义音效不存在，使用系统音效
            return self._play_system_sound(sound_name)
            
        except Exception as e:
            print(f"播放音效失败: {e}")
            return False
    
    def _play_wav_file(self, file_path: str) -> bool:
        """播放WAV文件"""
        try:
            # 使用wx.Sound播放音效
            sound = wx.Sound(file_path)
            if sound.IsOk():
                return sound.Play(wx.SOUND_ASYNC)
            return False
            
        except Exception as e:
            print(f"播放WAV文件失败: {e}")
            return False
    
    def _play_system_sound(self, sound_name: str) -> bool:
        """播放系统音效"""
        try:
            # 映射到系统音效
            system_sounds = {
                'notification': wx.SOUND_ASYNC,
                'task_complete': wx.SOUND_ASYNC,
                'task_add': wx.SOUND_ASYNC,
                'task_delete': wx.SOUND_ASYNC,
                'error': wx.SOUND_ASYNC,
                'success': wx.SOUND_ASYNC,
                'click': wx.SOUND_ASYNC,
                'hover': wx.SOUND_ASYNC
            }
            
            # 使用系统提示音
            wx.Bell()
            return True
            
        except Exception as e:
            print(f"播放系统音效失败: {e}")
            return False
    
    def play_notification_sound(self) -> bool:
        """播放通知音效"""
        return self.play_sound('notification')
    
    def play_task_complete_sound(self) -> bool:
        """播放任务完成音效"""
        return self.play_sound('task_complete')
    
    def play_task_add_sound(self) -> bool:
        """播放任务添加音效"""
        return self.play_sound('task_add')
    
    def play_task_delete_sound(self) -> bool:
        """播放任务删除音效"""
        return self.play_sound('task_delete')
    
    def play_error_sound(self) -> bool:
        """播放错误音效"""
        return self.play_sound('error')
    
    def play_success_sound(self) -> bool:
        """播放成功音效"""
        return self.play_sound('success')
    
    def play_click_sound(self) -> bool:
        """播放点击音效"""
        return self.play_sound('click')
    
    def play_hover_sound(self) -> bool:
        """播放悬停音效"""
        return self.play_sound('hover')
    
    def stop_all_sounds(self) -> None:
        """停止所有音效"""
        try:
            # 停止所有wx.Sound
            wx.Sound.Stop()
        except Exception as e:
            print(f"停止音效失败: {e}")
    
    def add_custom_sound(self, sound_name: str, file_path: str) -> bool:
        """添加自定义音效"""
        try:
            if os.path.exists(file_path):
                # 复制文件到音效目录
                import shutil
                target_path = os.path.join(self.sounds_dir, f"{sound_name}.wav")
                shutil.copy2(file_path, target_path)
                
                # 添加到音效列表
                self.sound_files[sound_name] = f"{sound_name}.wav"
                return True
            return False
            
        except Exception as e:
            print(f"添加自定义音效失败: {e}")
            return False
    
    def remove_custom_sound(self, sound_name: str) -> bool:
        """移除自定义音效"""
        try:
            if sound_name in self.sound_files:
                # 删除文件
                file_path = os.path.join(self.sounds_dir, self.sound_files[sound_name])
                if os.path.exists(file_path):
                    os.remove(file_path)
                
                # 从列表中移除
                del self.sound_files[sound_name]
                return True
            return False
            
        except Exception as e:
            print(f"移除自定义音效失败: {e}")
            return False
    
    def get_available_sounds(self) -> Dict[str, str]:
        """获取可用的音效列表"""
        return self.sound_files.copy()
    
    def test_sound(self, sound_name: str) -> bool:
        """测试音效播放"""
        print(f"测试播放音效: {sound_name}")
        return self.play_sound(sound_name, async_play=False)
    
    def create_sound_settings_panel(self, parent: wx.Window) -> wx.Panel:
        """创建音效设置面板"""
        panel = wx.Panel(parent)
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 启用音效复选框
        self.enable_checkbox = wx.CheckBox(panel, label="启用音效")
        self.enable_checkbox.SetValue(self.enabled)
        self.enable_checkbox.Bind(wx.EVT_CHECKBOX, self._on_enable_change)
        sizer.Add(self.enable_checkbox, 0, wx.ALL, 5)
        
        # 音量滑块
        volume_label = wx.StaticText(panel, label="音量:")
        sizer.Add(volume_label, 0, wx.ALL, 5)
        
        self.volume_slider = wx.Slider(panel, value=int(self.volume * 100), 
                                      minValue=0, maxValue=100)
        self.volume_slider.Bind(wx.EVT_SLIDER, self._on_volume_change)
        sizer.Add(self.volume_slider, 0, wx.EXPAND | wx.ALL, 5)
        
        # 测试按钮
        test_sizer = wx.BoxSizer(wx.HORIZONTAL)
        for sound_name in ['notification', 'task_complete', 'success', 'error']:
            btn = wx.Button(panel, label=f"测试{sound_name}", size=(80, 25))
            btn.Bind(wx.EVT_BUTTON, lambda evt, name=sound_name: self.test_sound(name))
            test_sizer.Add(btn, 0, wx.ALL, 2)
        
        sizer.Add(test_sizer, 0, wx.ALL, 5)
        
        panel.SetSizer(sizer)
        return panel
    
    def _on_enable_change(self, event) -> None:
        """启用状态改变事件"""
        self.set_enabled(event.IsChecked())
    
    def _on_volume_change(self, event) -> None:
        """音量改变事件"""
        self.set_volume(event.GetInt() / 100.0)
