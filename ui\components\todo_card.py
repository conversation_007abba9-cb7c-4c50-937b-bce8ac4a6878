"""
任务卡片组件
"""
import wx
from datetime import datetime
from typing import Optional, Callable
from ui.styles import UIStyles
from ui.animations import AnimationManager
from core.models import Todo


class TodoCard(wx.Panel):
    """任务卡片组件"""
    
    def __init__(self, parent, todo: Todo, animation_manager: AnimationManager,
                 on_complete_callback: Optional[Callable] = None,
                 on_edit_callback: Optional[Callable] = None,
                 on_delete_callback: Optional[Callable] = None,
                 theme: str = 'light'):
        """初始化任务卡片"""
        super().__init__(parent)
        
        self.todo = todo
        self.animation_manager = animation_manager
        self.on_complete_callback = on_complete_callback
        self.on_edit_callback = on_edit_callback
        self.on_delete_callback = on_delete_callback
        self.theme = theme
        
        # 状态变量
        self.is_hovered = False
        self.is_pressed = False
        
        # 初始化UI
        self.init_ui()
        self.setup_events()
    
    def init_ui(self) -> None:
        """初始化用户界面"""
        # 设置最小尺寸
        self.SetMinSize((400, 80))
        
        # 创建主布局
        main_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # 左侧：优先级条和复选框
        left_panel = wx.Panel(self)
        left_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # 优先级颜色条
        self.priority_bar = wx.Panel(left_panel, size=(4, -1))
        self.priority_bar.SetBackgroundColour(wx.Colour(self.todo.priority_color))
        left_sizer.Add(self.priority_bar, 0, wx.EXPAND | wx.RIGHT, UIStyles.SPACING['sm'])
        
        # 完成状态复选框
        self.checkbox = wx.CheckBox(left_panel)
        self.checkbox.SetValue(self.todo.completed)
        left_sizer.Add(self.checkbox, 0, wx.ALIGN_CENTER_VERTICAL)
        
        left_panel.SetSizer(left_sizer)
        main_sizer.Add(left_panel, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['sm'])
        
        # 中间：任务内容
        content_panel = wx.Panel(self)
        content_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 任务标题
        self.title_label = wx.StaticText(content_panel, label=self.todo.title)
        title_font = UIStyles.get_font('medium', bold=not self.todo.completed)
        self.title_label.SetFont(title_font)
        
        if self.todo.completed:
            # 已完成任务显示删除线效果
            self.title_label.SetForegroundColour(UIStyles.get_color('text_secondary', self.theme))
        else:
            self.title_label.SetForegroundColour(UIStyles.get_color('text_primary', self.theme))
        
        content_sizer.Add(self.title_label, 0, wx.EXPAND | wx.BOTTOM, UIStyles.SPACING['xs'])
        
        # 任务描述（如果有）
        if self.todo.description:
            self.desc_label = wx.StaticText(content_panel, label=self.todo.description)
            self.desc_label.SetFont(UIStyles.get_font('small'))
            self.desc_label.SetForegroundColour(UIStyles.get_color('text_secondary', self.theme))
            content_sizer.Add(self.desc_label, 0, wx.EXPAND | wx.BOTTOM, UIStyles.SPACING['xs'])
        
        # 到期时间（如果有）
        if self.todo.due_date:
            due_text = self.todo.due_date.strftime("%Y-%m-%d %H:%M")
            if self.todo.is_overdue:
                due_text += " (已过期)"
            
            self.due_label = wx.StaticText(content_panel, label=due_text)
            self.due_label.SetFont(UIStyles.get_font('small'))
            
            if self.todo.is_overdue:
                self.due_label.SetForegroundColour(UIStyles.get_color('error', self.theme))
            else:
                self.due_label.SetForegroundColour(UIStyles.get_color('text_secondary', self.theme))
            
            content_sizer.Add(self.due_label, 0, wx.EXPAND)
        
        content_panel.SetSizer(content_sizer)
        main_sizer.Add(content_panel, 1, wx.EXPAND | wx.ALL, UIStyles.SPACING['sm'])
        
        # 右侧：操作按钮
        button_panel = wx.Panel(self)
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # 编辑按钮
        self.edit_btn = wx.Button(button_panel, label="编辑", size=(60, 30))
        self.edit_btn.SetFont(UIStyles.get_font('small'))
        button_sizer.Add(self.edit_btn, 0, wx.RIGHT, UIStyles.SPACING['xs'])
        
        # 删除按钮
        self.delete_btn = wx.Button(button_panel, label="删除", size=(60, 30))
        self.delete_btn.SetFont(UIStyles.get_font('small'))
        button_sizer.Add(self.delete_btn, 0)
        
        button_panel.SetSizer(button_sizer)
        main_sizer.Add(button_panel, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, UIStyles.SPACING['sm'])
        
        self.SetSizer(main_sizer)
        
        # 应用样式
        self.apply_card_style()
    
    def apply_card_style(self) -> None:
        """应用卡片样式"""
        if self.todo.completed:
            bg_color = UIStyles.get_color('bg_secondary', self.theme)
        else:
            bg_color = UIStyles.get_color('bg_card', self.theme)
        
        self.SetBackgroundColour(bg_color)
        
        # 设置子面板背景色
        for child in self.GetChildren():
            if isinstance(child, wx.Panel):
                child.SetBackgroundColour(bg_color)
    
    def setup_events(self) -> None:
        """设置事件处理"""
        # 复选框事件
        self.checkbox.Bind(wx.EVT_CHECKBOX, self.on_checkbox_toggle)
        
        # 按钮事件
        self.edit_btn.Bind(wx.EVT_BUTTON, self.on_edit_click)
        self.delete_btn.Bind(wx.EVT_BUTTON, self.on_delete_click)
        
        # 鼠标事件
        self.Bind(wx.EVT_ENTER_WINDOW, self.on_mouse_enter)
        self.Bind(wx.EVT_LEAVE_WINDOW, self.on_mouse_leave)
        self.Bind(wx.EVT_LEFT_DOWN, self.on_mouse_down)
        self.Bind(wx.EVT_LEFT_UP, self.on_mouse_up)
        
        # 绘制事件
        self.Bind(wx.EVT_PAINT, self.on_paint)
    
    def on_checkbox_toggle(self, event) -> None:
        """复选框切换事件"""
        completed = self.checkbox.GetValue()
        
        if completed:
            # 播放完成动画
            self.animation_manager.completion_animation(self)
        
        # 更新任务状态
        self.todo.completed = completed
        
        # 调用回调函数
        if self.on_complete_callback:
            self.on_complete_callback(self.todo.id, completed)
        
        # 更新显示
        self.update_display()
    
    def on_edit_click(self, event) -> None:
        """编辑按钮点击事件"""
        if self.on_edit_callback:
            self.on_edit_callback(self.todo)
    
    def on_delete_click(self, event) -> None:
        """删除按钮点击事件"""
        # 确认删除
        dlg = wx.MessageDialog(self, f"确定要删除任务 '{self.todo.title}' 吗？", 
                              "确认删除", wx.YES_NO | wx.ICON_QUESTION)
        
        if dlg.ShowModal() == wx.ID_YES:
            # 播放删除动画
            self.animation_manager.slide_out(self, 'right', 300, 
                                           lambda: self._delete_callback())
        
        dlg.Destroy()
    
    def _delete_callback(self) -> None:
        """删除回调"""
        if self.on_delete_callback:
            self.on_delete_callback(self.todo.id)
    
    def on_mouse_enter(self, event) -> None:
        """鼠标进入事件"""
        self.is_hovered = True
        
        # 播放悬停动画
        self.animation_manager.scale_animation(self, 1.02, 150)
        
        self.Refresh()
    
    def on_mouse_leave(self, event) -> None:
        """鼠标离开事件"""
        self.is_hovered = False
        self.Refresh()
    
    def on_mouse_down(self, event) -> None:
        """鼠标按下事件"""
        self.is_pressed = True
        self.Refresh()
    
    def on_mouse_up(self, event) -> None:
        """鼠标释放事件"""
        self.is_pressed = False
        self.Refresh()
    
    def on_paint(self, event) -> None:
        """绘制事件"""
        dc = wx.PaintDC(self)
        self.draw_card(dc)
    
    def draw_card(self, dc: wx.PaintDC) -> None:
        """绘制卡片"""
        size = self.GetSize()
        
        # 创建图形上下文
        gc = wx.GraphicsContext.Create(dc)
        if not gc:
            return
        
        # 绘制阴影（如果悬停）
        if self.is_hovered:
            UIStyles.create_shadow_effect(gc, 2, 2, size.width - 4, size.height - 4, 
                                        UIStyles.BORDER_RADIUS['medium'])
        
        # 绘制卡片背景
        if self.todo.completed:
            bg_color = UIStyles.get_color('bg_secondary', self.theme)
        elif self.is_pressed:
            bg_color = UIStyles.get_color('bg_hover', self.theme)
        elif self.is_hovered:
            bg_color = UIStyles.get_color('bg_card', self.theme)
        else:
            bg_color = UIStyles.get_color('bg_card', self.theme)
        
        border_color = UIStyles.get_color('border_light', self.theme)
        
        UIStyles.draw_rounded_rect(gc, 0, 0, size.width, size.height,
                                 UIStyles.BORDER_RADIUS['medium'],
                                 bg_color, border_color)
    
    def update_display(self) -> None:
        """更新显示"""
        # 更新标题样式
        title_font = UIStyles.get_font('medium', bold=not self.todo.completed)
        self.title_label.SetFont(title_font)
        
        if self.todo.completed:
            self.title_label.SetForegroundColour(UIStyles.get_color('text_secondary', self.theme))
        else:
            self.title_label.SetForegroundColour(UIStyles.get_color('text_primary', self.theme))
        
        # 更新背景色
        self.apply_card_style()
        
        # 刷新显示
        self.Refresh()
        self.Layout()
    
    def update_todo(self, todo: Todo) -> None:
        """更新任务数据"""
        self.todo = todo
        
        # 更新UI元素
        self.title_label.SetLabel(todo.title)
        self.checkbox.SetValue(todo.completed)
        self.priority_bar.SetBackgroundColour(wx.Colour(todo.priority_color))
        
        # 更新描述
        if hasattr(self, 'desc_label') and todo.description:
            self.desc_label.SetLabel(todo.description)
        
        # 更新到期时间
        if hasattr(self, 'due_label') and todo.due_date:
            due_text = todo.due_date.strftime("%Y-%m-%d %H:%M")
            if todo.is_overdue:
                due_text += " (已过期)"
                self.due_label.SetForegroundColour(UIStyles.get_color('error', self.theme))
            else:
                self.due_label.SetForegroundColour(UIStyles.get_color('text_secondary', self.theme))
            
            self.due_label.SetLabel(due_text)
        
        # 更新显示
        self.update_display()
