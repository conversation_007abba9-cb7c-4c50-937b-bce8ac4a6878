"""
UI样式定义
"""
import wx


class UIStyles:
    """UI样式类"""

    # 当前主题
    _current_theme = 'light'

    # 颜色定义
    COLORS = {
        # 主色调
        'primary': '#3498db',
        'secondary': '#2ecc71',
        'accent': '#e74c3c',
        'warning': '#f39c12',
        
        # 背景色
        'bg_primary': '#ffffff',
        'bg_secondary': '#f8f9fa',
        'bg_dark': '#2c3e50',
        'bg_card': '#ffffff',
        'bg_hover': '#ecf0f1',
        
        # 文字色
        'text_primary': '#2c3e50',
        'text_secondary': '#7f8c8d',
        'text_light': '#bdc3c7',
        'text_white': '#ffffff',
        
        # 边框色
        'border_light': '#e9ecef',
        'border_medium': '#dee2e6',
        'border_dark': '#adb5bd',
        
        # 优先级颜色
        'priority_low': '#95a5a6',
        'priority_medium': '#f39c12',
        'priority_high': '#e74c3c',
        
        # 状态颜色
        'success': '#2ecc71',
        'error': '#e74c3c',
        'info': '#3498db',
    }
    
    # 暗色主题
    DARK_COLORS = {
        'bg_primary': '#2c3e50',
        'bg_secondary': '#34495e',
        'bg_card': '#34495e',
        'bg_hover': '#3d566e',
        'text_primary': '#ecf0f1',
        'text_secondary': '#bdc3c7',
        'border_light': '#4a6741',
        'border_medium': '#5d6d7e',
    }
    
    # 字体大小
    FONT_SIZES = {
        'small': 9,
        'normal': 11,
        'medium': 13,
        'large': 15,
        'xlarge': 18,
        'title': 20,
    }
    
    # 间距
    SPACING = {
        'xs': 4,
        'sm': 8,
        'md': 16,
        'lg': 24,
        'xl': 32,
    }
    
    # 圆角半径
    BORDER_RADIUS = {
        'small': 4,
        'medium': 8,
        'large': 12,
        'xlarge': 16,
    }
    
    @classmethod
    def set_theme(cls, theme: str) -> None:
        """设置当前主题"""
        cls._current_theme = theme

    @classmethod
    def get_current_theme(cls) -> str:
        """获取当前主题"""
        return cls._current_theme

    @classmethod
    def get_color(cls, color_name: str, theme: str = None) -> wx.Colour:
        """获取颜色对象"""
        if theme is None:
            theme = cls._current_theme
        colors = cls.DARK_COLORS if theme == 'dark' else cls.COLORS
        color_hex = colors.get(color_name, cls.COLORS.get(color_name, '#000000'))
        return wx.Colour(color_hex)
    
    @classmethod
    def get_font(cls, size: str = 'normal', bold: bool = False, family: int = wx.FONTFAMILY_DEFAULT) -> wx.Font:
        """获取字体对象"""
        font_size = cls.FONT_SIZES.get(size, cls.FONT_SIZES['normal'])
        weight = wx.FONTWEIGHT_BOLD if bold else wx.FONTWEIGHT_NORMAL
        return wx.Font(font_size, family, wx.FONTSTYLE_NORMAL, weight)
    
    @classmethod
    def create_rounded_rect_region(cls, width: int, height: int, radius: int) -> wx.Region:
        """创建圆角矩形区域"""
        # 创建圆角路径
        path = wx.GraphicsRenderer.GetDefaultRenderer().CreatePath()
        path.AddRoundedRectangle(0, 0, width, height, radius)
        return wx.Region(path)
    
    @classmethod
    def draw_rounded_rect(cls, gc: wx.GraphicsContext, x: int, y: int, 
                         width: int, height: int, radius: int, 
                         fill_color: wx.Colour, border_color: wx.Colour = None,
                         border_width: int = 1) -> None:
        """绘制圆角矩形"""
        # 设置填充色
        gc.SetBrush(wx.Brush(fill_color))
        
        # 设置边框
        if border_color:
            gc.SetPen(wx.Pen(border_color, border_width))
        else:
            gc.SetPen(wx.Pen(fill_color, 0))
        
        # 绘制圆角矩形
        gc.DrawRoundedRectangle(x, y, width, height, radius)
    
    @classmethod
    def create_shadow_effect(cls, gc: wx.GraphicsContext, x: int, y: int,
                           width: int, height: int, radius: int,
                           shadow_offset: int = 2, shadow_blur: int = 4) -> None:
        """创建阴影效果"""
        # 创建阴影颜色（半透明黑色）
        shadow_color = wx.Colour(0, 0, 0, 30)
        
        # 绘制多层阴影实现模糊效果
        for i in range(shadow_blur):
            alpha = 30 - (i * 5)
            if alpha <= 0:
                break
            
            shadow_color.Set(0, 0, 0, alpha)
            gc.SetBrush(wx.Brush(shadow_color))
            gc.SetPen(wx.Pen(shadow_color, 0))
            
            offset = shadow_offset + i
            gc.DrawRoundedRectangle(
                x + offset, y + offset, 
                width, height, radius
            )
