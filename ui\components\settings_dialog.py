"""
设置对话框
"""
import wx
import wx.lib.scrolledpanel as scrolled
from typing import Dict, Any, Callable, Optional
from ui.styles import UIStyles


class SettingsDialog(wx.Dialog):
    """设置对话框"""
    
    def __init__(self, parent, current_settings: Dict[str, Any] = None, theme='light'):
        """初始化设置对话框"""
        super().__init__(parent, title="设置", size=(600, 500),
                        style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER)
        
        self.theme = theme
        self.current_settings = current_settings or {}
        self.settings_data = {}
        
        # 初始化UI
        self.init_ui()
        self.setup_events()
        self.load_current_settings()
        
        # 居中显示
        self.Center()
    
    def init_ui(self) -> None:
        """初始化用户界面"""
        # 创建主面板
        main_panel = wx.Panel(self)
        main_panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.theme))
        
        # 创建主布局
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题
        title_label = wx.StaticText(main_panel, label="应用设置")
        title_label.SetFont(UIStyles.get_font('large', bold=True))
        title_label.SetForegroundColour(UIStyles.get_color('text_primary', self.theme))
        main_sizer.Add(title_label, 0, wx.ALL | wx.CENTER, UIStyles.SPACING['lg'])
        
        # 创建笔记本控件（标签页）
        self.notebook = wx.Notebook(main_panel)
        
        # 创建各个设置页面
        self.create_general_page()
        self.create_appearance_page()
        self.create_notification_page()
        self.create_advanced_page()
        
        main_sizer.Add(self.notebook, 1, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        # 按钮区域
        button_panel = self.create_button_panel(main_panel)
        main_sizer.Add(button_panel, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        main_panel.SetSizer(main_sizer)
        
        # 设置对话框布局
        dialog_sizer = wx.BoxSizer(wx.VERTICAL)
        dialog_sizer.Add(main_panel, 1, wx.EXPAND)
        self.SetSizer(dialog_sizer)
    
    def create_general_page(self) -> None:
        """创建常规设置页面"""
        panel = scrolled.ScrolledPanel(self.notebook)
        panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.theme))
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 开机自启
        autostart_box = wx.StaticBox(panel, label="启动设置")
        autostart_sizer = wx.StaticBoxSizer(autostart_box, wx.VERTICAL)
        
        self.autostart_checkbox = wx.CheckBox(panel, label="开机自动启动")
        autostart_sizer.Add(self.autostart_checkbox, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.minimize_to_tray_checkbox = wx.CheckBox(panel, label="启动时最小化到系统托盘")
        autostart_sizer.Add(self.minimize_to_tray_checkbox, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        sizer.Add(autostart_sizer, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        # 窗口设置
        window_box = wx.StaticBox(panel, label="窗口设置")
        window_sizer = wx.StaticBoxSizer(window_box, wx.VERTICAL)
        
        self.remember_window_checkbox = wx.CheckBox(panel, label="记住窗口位置和大小")
        window_sizer.Add(self.remember_window_checkbox, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.always_on_top_checkbox = wx.CheckBox(panel, label="窗口总是置顶")
        window_sizer.Add(self.always_on_top_checkbox, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        sizer.Add(window_sizer, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        # 数据设置
        data_box = wx.StaticBox(panel, label="数据管理")
        data_sizer = wx.StaticBoxSizer(data_box, wx.VERTICAL)
        
        self.auto_backup_checkbox = wx.CheckBox(panel, label="自动备份数据")
        data_sizer.Add(self.auto_backup_checkbox, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        backup_btn = wx.Button(panel, label="立即备份", size=(120, 30))
        data_sizer.Add(backup_btn, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        sizer.Add(data_sizer, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        panel.SetSizer(sizer)
        panel.SetupScrolling()
        self.notebook.AddPage(panel, "常规")
    
    def create_appearance_page(self) -> None:
        """创建外观设置页面"""
        panel = scrolled.ScrolledPanel(self.notebook)
        panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.theme))
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 主题设置
        theme_box = wx.StaticBox(panel, label="主题设置")
        theme_sizer = wx.StaticBoxSizer(theme_box, wx.VERTICAL)
        
        theme_label = wx.StaticText(panel, label="选择主题:")
        theme_sizer.Add(theme_label, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.theme_choice = wx.Choice(panel, choices=["明亮主题", "暗黑主题", "自动"])
        theme_sizer.Add(self.theme_choice, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['sm'])
        
        sizer.Add(theme_sizer, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        # 字体设置
        font_box = wx.StaticBox(panel, label="字体设置")
        font_sizer = wx.StaticBoxSizer(font_box, wx.VERTICAL)
        
        font_size_label = wx.StaticText(panel, label="字体大小:")
        font_sizer.Add(font_size_label, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.font_size_choice = wx.Choice(panel, choices=["小", "正常", "大", "特大"])
        font_sizer.Add(self.font_size_choice, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['sm'])
        
        sizer.Add(font_sizer, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        # 动画设置
        animation_box = wx.StaticBox(panel, label="动画设置")
        animation_sizer = wx.StaticBoxSizer(animation_box, wx.VERTICAL)
        
        self.enable_animations_checkbox = wx.CheckBox(panel, label="启用界面动画")
        animation_sizer.Add(self.enable_animations_checkbox, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        animation_speed_label = wx.StaticText(panel, label="动画速度:")
        animation_sizer.Add(animation_speed_label, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.animation_speed_slider = wx.Slider(panel, value=50, minValue=10, maxValue=100)
        animation_sizer.Add(self.animation_speed_slider, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['sm'])
        
        sizer.Add(animation_sizer, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        panel.SetSizer(sizer)
        panel.SetupScrolling()
        self.notebook.AddPage(panel, "外观")
    
    def create_notification_page(self) -> None:
        """创建通知设置页面"""
        panel = scrolled.ScrolledPanel(self.notebook)
        panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.theme))
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 通知设置
        notification_box = wx.StaticBox(panel, label="通知设置")
        notification_sizer = wx.StaticBoxSizer(notification_box, wx.VERTICAL)
        
        self.enable_notifications_checkbox = wx.CheckBox(panel, label="启用系统通知")
        notification_sizer.Add(self.enable_notifications_checkbox, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.notification_sound_checkbox = wx.CheckBox(panel, label="播放通知音效")
        notification_sizer.Add(self.notification_sound_checkbox, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        sizer.Add(notification_sizer, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        # 提醒设置
        reminder_box = wx.StaticBox(panel, label="提醒设置")
        reminder_sizer = wx.StaticBoxSizer(reminder_box, wx.VERTICAL)
        
        reminder_time_label = wx.StaticText(panel, label="提前提醒时间（分钟）:")
        reminder_sizer.Add(reminder_time_label, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.reminder_time_spin = wx.SpinCtrl(panel, value="15", min=1, max=1440)
        reminder_sizer.Add(self.reminder_time_spin, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['sm'])
        
        self.daily_summary_checkbox = wx.CheckBox(panel, label="每日任务总结")
        reminder_sizer.Add(self.daily_summary_checkbox, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        summary_time_label = wx.StaticText(panel, label="总结时间:")
        reminder_sizer.Add(summary_time_label, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.summary_time_ctrl = wx.TextCtrl(panel, value="09:00")
        reminder_sizer.Add(self.summary_time_ctrl, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['sm'])
        
        sizer.Add(reminder_sizer, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        panel.SetSizer(sizer)
        panel.SetupScrolling()
        self.notebook.AddPage(panel, "通知")
    
    def create_advanced_page(self) -> None:
        """创建高级设置页面"""
        panel = scrolled.ScrolledPanel(self.notebook)
        panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.theme))
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 快捷键设置
        hotkey_box = wx.StaticBox(panel, label="快捷键设置")
        hotkey_sizer = wx.StaticBoxSizer(hotkey_box, wx.VERTICAL)
        
        self.global_hotkey_checkbox = wx.CheckBox(panel, label="启用全局快捷键")
        hotkey_sizer.Add(self.global_hotkey_checkbox, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        hotkey_label = wx.StaticText(panel, label="显示/隐藏窗口:")
        hotkey_sizer.Add(hotkey_label, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.hotkey_ctrl = wx.TextCtrl(panel, value="Ctrl+Alt+T")
        hotkey_sizer.Add(self.hotkey_ctrl, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['sm'])
        
        sizer.Add(hotkey_sizer, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        # 性能设置
        performance_box = wx.StaticBox(panel, label="性能设置")
        performance_sizer = wx.StaticBoxSizer(performance_box, wx.VERTICAL)
        
        self.hardware_acceleration_checkbox = wx.CheckBox(panel, label="启用硬件加速")
        performance_sizer.Add(self.hardware_acceleration_checkbox, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.reduce_animations_checkbox = wx.CheckBox(panel, label="减少动画以提升性能")
        performance_sizer.Add(self.reduce_animations_checkbox, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        sizer.Add(performance_sizer, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        # 调试设置
        debug_box = wx.StaticBox(panel, label="调试设置")
        debug_sizer = wx.StaticBoxSizer(debug_box, wx.VERTICAL)
        
        self.debug_mode_checkbox = wx.CheckBox(panel, label="启用调试模式")
        debug_sizer.Add(self.debug_mode_checkbox, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.log_level_label = wx.StaticText(panel, label="日志级别:")
        debug_sizer.Add(self.log_level_label, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.log_level_choice = wx.Choice(panel, choices=["错误", "警告", "信息", "调试"])
        debug_sizer.Add(self.log_level_choice, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['sm'])
        
        sizer.Add(debug_sizer, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        panel.SetSizer(sizer)
        panel.SetupScrolling()
        self.notebook.AddPage(panel, "高级")
    
    def create_button_panel(self, parent) -> wx.Panel:
        """创建按钮面板"""
        button_panel = wx.Panel(parent)
        button_panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.theme))
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # 重置按钮
        reset_btn = wx.Button(button_panel, label="重置默认", size=(100, 35))
        reset_btn.SetFont(UIStyles.get_font('medium'))
        button_sizer.Add(reset_btn, 0, wx.RIGHT, UIStyles.SPACING['sm'])
        
        # 添加弹性空间
        button_sizer.AddStretchSpacer()
        
        # 取消按钮
        cancel_btn = wx.Button(button_panel, wx.ID_CANCEL, "取消", size=(100, 35))
        cancel_btn.SetFont(UIStyles.get_font('medium'))
        button_sizer.Add(cancel_btn, 0, wx.RIGHT, UIStyles.SPACING['sm'])
        
        # 应用按钮
        apply_btn = wx.Button(button_panel, wx.ID_APPLY, "应用", size=(100, 35))
        apply_btn.SetFont(UIStyles.get_font('medium'))
        button_sizer.Add(apply_btn, 0, wx.RIGHT, UIStyles.SPACING['sm'])
        
        # 确定按钮
        ok_btn = wx.Button(button_panel, wx.ID_OK, "确定", size=(100, 35))
        ok_btn.SetFont(UIStyles.get_font('medium', bold=True))
        ok_btn.SetDefault()
        button_sizer.Add(ok_btn, 0)
        
        button_panel.SetSizer(button_sizer)
        
        # 绑定事件
        reset_btn.Bind(wx.EVT_BUTTON, self.on_reset)
        
        return button_panel
    
    def setup_events(self) -> None:
        """设置事件处理"""
        self.Bind(wx.EVT_BUTTON, self.on_ok, id=wx.ID_OK)
        self.Bind(wx.EVT_BUTTON, self.on_apply, id=wx.ID_APPLY)
        self.Bind(wx.EVT_BUTTON, self.on_cancel, id=wx.ID_CANCEL)
    
    def load_current_settings(self) -> None:
        """加载当前设置"""
        # 常规设置
        self.autostart_checkbox.SetValue(self.current_settings.get('auto_start', False))
        self.minimize_to_tray_checkbox.SetValue(self.current_settings.get('minimize_to_tray', False))
        self.remember_window_checkbox.SetValue(self.current_settings.get('remember_window', True))
        self.always_on_top_checkbox.SetValue(self.current_settings.get('always_on_top', False))
        self.auto_backup_checkbox.SetValue(self.current_settings.get('auto_backup', True))
        
        # 外观设置
        theme_map = {'light': 0, 'dark': 1, 'auto': 2}
        self.theme_choice.SetSelection(theme_map.get(self.current_settings.get('theme', 'light'), 0))
        
        font_size_map = {'small': 0, 'normal': 1, 'large': 2, 'xlarge': 3}
        self.font_size_choice.SetSelection(font_size_map.get(self.current_settings.get('font_size', 'normal'), 1))
        
        self.enable_animations_checkbox.SetValue(self.current_settings.get('enable_animations', True))
        self.animation_speed_slider.SetValue(self.current_settings.get('animation_speed', 50))
        
        # 通知设置
        self.enable_notifications_checkbox.SetValue(self.current_settings.get('notifications_enabled', True))
        self.notification_sound_checkbox.SetValue(self.current_settings.get('sound_enabled', True))
        self.reminder_time_spin.SetValue(self.current_settings.get('reminder_time', 15))
        self.daily_summary_checkbox.SetValue(self.current_settings.get('daily_summary', True))
        self.summary_time_ctrl.SetValue(self.current_settings.get('summary_time', '09:00'))
        
        # 高级设置
        self.global_hotkey_checkbox.SetValue(self.current_settings.get('global_hotkey', False))
        self.hotkey_ctrl.SetValue(self.current_settings.get('hotkey', 'Ctrl+Alt+T'))
        self.hardware_acceleration_checkbox.SetValue(self.current_settings.get('hardware_acceleration', True))
        self.reduce_animations_checkbox.SetValue(self.current_settings.get('reduce_animations', False))
        self.debug_mode_checkbox.SetValue(self.current_settings.get('debug_mode', False))
        
        log_level_map = {'error': 0, 'warning': 1, 'info': 2, 'debug': 3}
        self.log_level_choice.SetSelection(log_level_map.get(self.current_settings.get('log_level', 'info'), 2))
    
    def collect_settings(self) -> None:
        """收集设置数据"""
        # 常规设置
        self.settings_data['auto_start'] = self.autostart_checkbox.GetValue()
        self.settings_data['minimize_to_tray'] = self.minimize_to_tray_checkbox.GetValue()
        self.settings_data['remember_window'] = self.remember_window_checkbox.GetValue()
        self.settings_data['always_on_top'] = self.always_on_top_checkbox.GetValue()
        self.settings_data['auto_backup'] = self.auto_backup_checkbox.GetValue()
        
        # 外观设置
        theme_choices = ['light', 'dark', 'auto']
        self.settings_data['theme'] = theme_choices[self.theme_choice.GetSelection()]
        
        font_size_choices = ['small', 'normal', 'large', 'xlarge']
        self.settings_data['font_size'] = font_size_choices[self.font_size_choice.GetSelection()]
        
        self.settings_data['enable_animations'] = self.enable_animations_checkbox.GetValue()
        self.settings_data['animation_speed'] = self.animation_speed_slider.GetValue()
        
        # 通知设置
        self.settings_data['notifications_enabled'] = self.enable_notifications_checkbox.GetValue()
        self.settings_data['sound_enabled'] = self.notification_sound_checkbox.GetValue()
        self.settings_data['reminder_time'] = self.reminder_time_spin.GetValue()
        self.settings_data['daily_summary'] = self.daily_summary_checkbox.GetValue()
        self.settings_data['summary_time'] = self.summary_time_ctrl.GetValue()
        
        # 高级设置
        self.settings_data['global_hotkey'] = self.global_hotkey_checkbox.GetValue()
        self.settings_data['hotkey'] = self.hotkey_ctrl.GetValue()
        self.settings_data['hardware_acceleration'] = self.hardware_acceleration_checkbox.GetValue()
        self.settings_data['reduce_animations'] = self.reduce_animations_checkbox.GetValue()
        self.settings_data['debug_mode'] = self.debug_mode_checkbox.GetValue()
        
        log_level_choices = ['error', 'warning', 'info', 'debug']
        self.settings_data['log_level'] = log_level_choices[self.log_level_choice.GetSelection()]
    
    def on_ok(self, event) -> None:
        """确定按钮事件"""
        self.collect_settings()
        self.EndModal(wx.ID_OK)
    
    def on_apply(self, event) -> None:
        """应用按钮事件"""
        self.collect_settings()
        # 立即应用主题设置
        if hasattr(self.GetParent(), 'apply_settings'):
            self.GetParent().apply_settings(self.settings_data)
        # 触发应用设置事件
        apply_event = wx.CommandEvent(wx.wxEVT_COMMAND_BUTTON_CLICKED, wx.ID_APPLY)
        wx.PostEvent(self.GetParent(), apply_event)
    
    def on_cancel(self, event) -> None:
        """取消按钮事件"""
        self.EndModal(wx.ID_CANCEL)
    
    def on_reset(self, event) -> None:
        """重置按钮事件"""
        dlg = wx.MessageDialog(
            self,
            "确定要重置所有设置为默认值吗？",
            "确认重置",
            wx.YES_NO | wx.ICON_QUESTION
        )
        
        if dlg.ShowModal() == wx.ID_YES:
            self.reset_to_defaults()
        
        dlg.Destroy()
    
    def reset_to_defaults(self) -> None:
        """重置为默认设置"""
        # 重置所有控件为默认值
        self.autostart_checkbox.SetValue(False)
        self.minimize_to_tray_checkbox.SetValue(False)
        self.remember_window_checkbox.SetValue(True)
        self.always_on_top_checkbox.SetValue(False)
        self.auto_backup_checkbox.SetValue(True)
        
        self.theme_choice.SetSelection(0)  # 明亮主题
        self.font_size_choice.SetSelection(1)  # 正常
        self.enable_animations_checkbox.SetValue(True)
        self.animation_speed_slider.SetValue(50)
        
        self.enable_notifications_checkbox.SetValue(True)
        self.notification_sound_checkbox.SetValue(True)
        self.reminder_time_spin.SetValue(15)
        self.daily_summary_checkbox.SetValue(True)
        self.summary_time_ctrl.SetValue("09:00")
        
        self.global_hotkey_checkbox.SetValue(False)
        self.hotkey_ctrl.SetValue("Ctrl+Alt+T")
        self.hardware_acceleration_checkbox.SetValue(True)
        self.reduce_animations_checkbox.SetValue(False)
        self.debug_mode_checkbox.SetValue(False)
        self.log_level_choice.SetSelection(2)  # 信息
    
    def get_settings_data(self) -> Dict[str, Any]:
        """获取设置数据"""
        return self.settings_data
