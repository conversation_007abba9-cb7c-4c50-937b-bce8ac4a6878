"""
对话框尺寸测试程序
"""
import wx
import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟数据模型
class MockCategory:
    def __init__(self, id, name, color="#3498db", icon="💼"):
        self.id = id
        self.name = name
        self.color = color
        self.icon = icon

class MockFolder:
    def __init__(self, id, name, color="#95a5a6"):
        self.id = id
        self.name = name
        self.color = color

class MockTodo:
    def __init__(self, id=1, title="测试任务", description="测试描述", priority=2):
        self.id = id
        self.title = title
        self.description = description
        self.priority = priority
        self.category_id = 1
        self.folder_id = 1
        self.due_date = None
        self.completed = False


class DialogSizeTestWindow(wx.Frame):
    """对话框尺寸测试窗口"""
    
    def __init__(self):
        super().__init__(None, title="对话框尺寸测试", size=(800, 600))
        
        # 模拟数据
        self.categories = [
            MockCategory(1, "工作", "#e74c3c", "💼"),
            MockCategory(2, "个人", "#2ecc71", "👤"),
            MockCategory(3, "学习", "#f39c12", "📚"),
        ]
        
        self.folders = [
            MockFolder(1, "重要", "#e74c3c"),
            MockFolder(2, "今天", "#f39c12"),
        ]
        
        self.current_theme = 'light'
        
        self.init_ui()
        self.Center()
    
    def init_ui(self):
        """初始化用户界面"""
        panel = wx.Panel(self)
        panel.SetBackgroundColour(wx.Colour("#f8f9fa"))
        
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题
        title = wx.StaticText(panel, label="对话框尺寸测试")
        title.SetFont(wx.Font(16, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        sizer.Add(title, 0, wx.ALL | wx.CENTER, 20)
        
        # 说明文本
        info_text = wx.StaticText(panel, label="测试各个对话框的尺寸是否合适，确保所有内容都能完整显示")
        info_text.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        sizer.Add(info_text, 0, wx.ALL | wx.CENTER, 10)
        
        # 测试按钮
        button_sizer = wx.GridSizer(2, 3, 15, 15)
        
        # 分类管理对话框测试
        category_mgr_btn = wx.Button(panel, label="测试分类管理对话框", size=(200, 60))
        category_mgr_btn.Bind(wx.EVT_BUTTON, self.test_category_manager_dialog)
        button_sizer.Add(category_mgr_btn, 0, wx.EXPAND)
        
        # 新建任务对话框测试
        add_todo_btn = wx.Button(panel, label="测试新建任务对话框", size=(200, 60))
        add_todo_btn.Bind(wx.EVT_BUTTON, self.test_add_todo_dialog)
        button_sizer.Add(add_todo_btn, 0, wx.EXPAND)
        
        # 编辑任务对话框测试
        edit_todo_btn = wx.Button(panel, label="测试编辑任务对话框", size=(200, 60))
        edit_todo_btn.Bind(wx.EVT_BUTTON, self.test_edit_todo_dialog)
        button_sizer.Add(edit_todo_btn, 0, wx.EXPAND)
        
        # 尺寸检查测试
        size_check_btn = wx.Button(panel, label="检查对话框尺寸", size=(200, 60))
        size_check_btn.Bind(wx.EVT_BUTTON, self.check_dialog_sizes)
        button_sizer.Add(size_check_btn, 0, wx.EXPAND)
        
        # 屏幕适配测试
        screen_test_btn = wx.Button(panel, label="测试屏幕适配", size=(200, 60))
        screen_test_btn.Bind(wx.EVT_BUTTON, self.test_screen_adaptation)
        button_sizer.Add(screen_test_btn, 0, wx.EXPAND)
        
        # 综合测试
        comprehensive_btn = wx.Button(panel, label="综合测试", size=(200, 60))
        comprehensive_btn.Bind(wx.EVT_BUTTON, self.comprehensive_test)
        button_sizer.Add(comprehensive_btn, 0, wx.EXPAND)
        
        sizer.Add(button_sizer, 0, wx.ALL | wx.CENTER, 20)
        
        # 结果显示区域
        self.result_text = wx.TextCtrl(panel, style=wx.TE_MULTILINE | wx.TE_READONLY, size=(-1, 250))
        self.result_text.SetFont(wx.Font(9, wx.FONTFAMILY_TELETYPE, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        sizer.Add(self.result_text, 1, wx.EXPAND | wx.ALL, 20)
        
        panel.SetSizer(sizer)
    
    def log_result(self, test_name, result, details=""):
        """记录测试结果"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        status = "✅ 通过" if result else "❌ 失败"
        log_text = f"[{timestamp}] {test_name}: {status}\n"
        if details:
            log_text += f"详情: {details}\n"
        log_text += "-" * 60 + "\n"
        
        self.result_text.AppendText(log_text)
    
    def test_category_manager_dialog(self, event):
        """测试分类管理对话框"""
        try:
            from ui.components.category_manager_dialog import CategoryManagerDialog
            dlg = CategoryManagerDialog(self, self.categories, self.current_theme)
            
            size = dlg.GetSize()
            min_size = dlg.GetMinSize()
            
            # 检查尺寸是否合适
            width_ok = size[0] >= 700  # 宽度应该至少700
            height_ok = size[1] >= 650  # 高度应该至少650
            min_width_ok = min_size[0] >= 700
            min_height_ok = min_size[1] >= 650
            
            all_ok = width_ok and height_ok and min_width_ok and min_height_ok
            
            details = f"尺寸: {size} (宽{'✓' if width_ok else '✗'}, 高{'✓' if height_ok else '✗'}), 最小: {min_size} (宽{'✓' if min_width_ok else '✗'}, 高{'✓' if min_height_ok else '✗'})"
            
            # 显示对话框供用户检查
            result = dlg.ShowModal()
            dlg.Destroy()
            
            self.log_result("分类管理对话框", all_ok, f"{details}, 用户操作: {'确定' if result == wx.ID_OK else '取消'}")
            
        except Exception as e:
            self.log_result("分类管理对话框", False, str(e))
    
    def test_add_todo_dialog(self, event):
        """测试新建任务对话框"""
        try:
            from ui.components.add_todo_dialog import AddTodoDialog
            dlg = AddTodoDialog(self, self.categories, self.folders, self.current_theme)
            
            size = dlg.GetSize()
            min_size = dlg.GetMinSize()
            
            # 检查尺寸是否合适
            width_ok = size[0] >= 800  # 宽度应该至少800
            height_ok = size[1] >= 530  # 高度应该至少530
            min_width_ok = min_size[0] >= 800
            min_height_ok = min_size[1] >= 530
            
            # 检查是否是横向布局
            is_horizontal = size[0] > size[1]
            
            all_ok = width_ok and height_ok and min_width_ok and min_height_ok and is_horizontal
            
            details = f"尺寸: {size} (宽{'✓' if width_ok else '✗'}, 高{'✓' if height_ok else '✗'}), 最小: {min_size} (宽{'✓' if min_width_ok else '✗'}, 高{'✓' if min_height_ok else '✗'}), 横向: {'✓' if is_horizontal else '✗'}"
            
            # 显示对话框供用户检查
            result = dlg.ShowModal()
            dlg.Destroy()
            
            self.log_result("新建任务对话框", all_ok, f"{details}, 用户操作: {'确定' if result == wx.ID_OK else '取消'}")
            
        except Exception as e:
            self.log_result("新建任务对话框", False, str(e))
    
    def test_edit_todo_dialog(self, event):
        """测试编辑任务对话框"""
        try:
            from ui.components.edit_todo_dialog import EditTodoDialog
            test_todo = MockTodo()
            dlg = EditTodoDialog(self, test_todo, self.categories, self.folders, self.current_theme)
            
            size = dlg.GetSize()
            min_size = dlg.GetMinSize()
            
            # 检查尺寸是否合适（应该与新建任务对话框相同）
            width_ok = size[0] >= 800
            height_ok = size[1] >= 530
            min_width_ok = min_size[0] >= 800
            min_height_ok = min_size[1] >= 530
            
            is_horizontal = size[0] > size[1]
            
            all_ok = width_ok and height_ok and min_width_ok and min_height_ok and is_horizontal
            
            details = f"尺寸: {size} (宽{'✓' if width_ok else '✗'}, 高{'✓' if height_ok else '✗'}), 最小: {min_size} (宽{'✓' if min_width_ok else '✗'}, 高{'✓' if min_height_ok else '✗'}), 横向: {'✓' if is_horizontal else '✗'}"
            
            # 显示对话框供用户检查
            result = dlg.ShowModal()
            dlg.Destroy()
            
            self.log_result("编辑任务对话框", all_ok, f"{details}, 用户操作: {'确定' if result == wx.ID_OK else '取消'}")
            
        except Exception as e:
            self.log_result("编辑任务对话框", False, str(e))
    
    def check_dialog_sizes(self, event):
        """检查对话框尺寸"""
        try:
            # 获取屏幕尺寸
            screen_size = wx.GetDisplaySize()
            
            # 检查各对话框的尺寸设置
            dialogs_info = [
                ("分类管理对话框", (750, 700), (700, 650)),
                ("新建任务对话框", (850, 580), (800, 530)),
                ("编辑任务对话框", (850, 580), (800, 530)),  # 继承自新建任务对话框
            ]
            
            results = []
            for name, default_size, min_size in dialogs_info:
                # 检查是否适合屏幕
                fits_screen = (default_size[0] < screen_size[0] * 0.9 and 
                              default_size[1] < screen_size[1] * 0.9)
                
                # 检查最小尺寸是否合理
                min_reasonable = (min_size[0] >= 600 and min_size[1] >= 400)
                
                status = "✓" if fits_screen and min_reasonable else "✗"
                results.append(f"{name}: {default_size} (最小: {min_size}) {status}")
            
            details = f"屏幕尺寸: {screen_size}\n" + "\n".join(results)
            self.log_result("对话框尺寸检查", True, details)
            
        except Exception as e:
            self.log_result("对话框尺寸检查", False, str(e))
    
    def test_screen_adaptation(self, event):
        """测试屏幕适配"""
        try:
            screen_size = wx.GetDisplaySize()
            
            # 模拟不同屏幕尺寸
            test_sizes = [
                ("1024x768", (1024, 768)),
                ("1366x768", (1366, 768)),
                ("1920x1080", (1920, 1080)),
                ("当前屏幕", screen_size),
            ]
            
            results = []
            for name, size in test_sizes:
                # 检查对话框是否适合这个屏幕尺寸
                category_fits = (750 < size[0] * 0.9 and 700 < size[1] * 0.9)
                todo_fits = (850 < size[0] * 0.9 and 580 < size[1] * 0.9)
                
                status = "✓" if category_fits and todo_fits else "✗"
                results.append(f"{name} {size}: 分类管理{'✓' if category_fits else '✗'}, 任务对话框{'✓' if todo_fits else '✗'} {status}")
            
            details = "\n".join(results)
            self.log_result("屏幕适配测试", True, details)
            
        except Exception as e:
            self.log_result("屏幕适配测试", False, str(e))
    
    def comprehensive_test(self, event):
        """综合测试"""
        self.log_result("综合测试", True, "开始综合测试所有对话框...")
        
        # 依次执行各项测试
        wx.CallLater(100, self._comprehensive_step_1)
    
    def _comprehensive_step_1(self):
        self.check_dialog_sizes(None)
        wx.CallLater(500, self._comprehensive_step_2)
    
    def _comprehensive_step_2(self):
        self.test_screen_adaptation(None)
        self.log_result("综合测试", True, "综合测试完成！请手动测试各对话框的显示效果。")


class DialogSizeTestApp(wx.App):
    """对话框尺寸测试应用"""
    
    def OnInit(self):
        try:
            self.main_window = DialogSizeTestWindow()
            self.main_window.Show()
            self.SetTopWindow(self.main_window)
            return True
        except Exception as e:
            wx.MessageBox(f"应用启动失败: {str(e)}", "错误", wx.OK | wx.ICON_ERROR)
            return False


def main():
    """主函数"""
    app = DialogSizeTestApp()
    app.MainLoop()


if __name__ == "__main__":
    main()
