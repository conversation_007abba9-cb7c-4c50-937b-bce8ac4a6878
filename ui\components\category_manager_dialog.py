"""
分类管理对话框
"""
import wx
import wx.lib.colourselect as csel
from typing import List, Dict, Any, Optional
from ui.styles import UIStyles
from core.models import Category


class CategoryManagerDialog(wx.Dialog):
    """分类管理对话框"""
    
    def __init__(self, parent, categories: List[Category] = None, theme='light'):
        """初始化分类管理对话框"""
        super().__init__(parent, title="分类管理", size=(750, 700),
                        style=wx.DEFAULT_DIALOG_STYLE | wx.RESIZE_BORDER)

        # 设置最小尺寸，确保所有内容都能完整显示
        self.SetMinSize((700, 850))

        self.theme = theme
        self.categories = categories or []
        self.modified_categories = []
        self.deleted_categories = []
        self.new_categories = []  # 新增分类列表
        self.parent_window = parent  # 保存父窗口引用，用于立即保存
        
        # 预定义图标
        self.available_icons = [
            "💼", "👤", "📚", "🛒", "🏠", "💻", "🎯", "📝",
            "📞", "✈️", "🏥", "🎵", "🎮", "🍽️", "🚗", "💰"
        ]
        
        # 初始化UI
        self.init_ui()
        self.setup_events()
        self.load_categories()
        
        # 居中显示
        self.Center()
    
    def init_ui(self) -> None:
        """初始化用户界面"""
        # 创建主面板
        main_panel = wx.Panel(self)
        main_panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.theme))
        
        # 创建主布局
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题
        title_label = wx.StaticText(main_panel, label="分类管理")
        title_label.SetFont(UIStyles.get_font('large', bold=True))
        title_label.SetForegroundColour(UIStyles.get_color('text_primary', self.theme))
        main_sizer.Add(title_label, 0, wx.ALL | wx.CENTER, UIStyles.SPACING['md'])
        
        # 创建水平分割器
        splitter = wx.SplitterWindow(main_panel, style=wx.SP_3D | wx.SP_LIVE_UPDATE)
        
        # 左侧：分类列表
        self.create_category_list(splitter)
        
        # 右侧：分类编辑
        self.create_category_editor(splitter)
        
        # 设置分割器
        splitter.SplitVertically(self.list_panel, self.editor_panel, 280)
        splitter.SetMinimumPaneSize(220)
        
        main_sizer.Add(splitter, 1, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        # 按钮区域
        button_panel = self.create_button_panel(main_panel)
        main_sizer.Add(button_panel, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
        
        main_panel.SetSizer(main_sizer)
        
        # 设置对话框布局
        dialog_sizer = wx.BoxSizer(wx.VERTICAL)
        dialog_sizer.Add(main_panel, 1, wx.EXPAND)
        self.SetSizer(dialog_sizer)
    
    def create_category_list(self, parent) -> None:
        """创建分类列表面板"""
        self.list_panel = wx.Panel(parent)
        self.list_panel.SetBackgroundColour(UIStyles.get_color('bg_secondary', self.theme))
        list_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 列表标题
        list_title = wx.StaticText(self.list_panel, label="分类列表")
        list_title.SetFont(UIStyles.get_font('medium', bold=True))
        list_sizer.Add(list_title, 0, wx.ALL, UIStyles.SPACING['md'])
        
        # 分类列表控件
        self.category_listbox = wx.ListBox(self.list_panel, style=wx.LB_SINGLE)
        list_sizer.Add(self.category_listbox, 1, wx.EXPAND | wx.ALL, UIStyles.SPACING['sm'])
        
        # 列表操作按钮
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        self.add_btn = wx.Button(self.list_panel, label="添加", size=(60, 30))
        self.add_btn.SetFont(UIStyles.get_font('small'))
        button_sizer.Add(self.add_btn, 0, wx.RIGHT, UIStyles.SPACING['xs'])
        
        self.delete_btn = wx.Button(self.list_panel, label="删除", size=(60, 30))
        self.delete_btn.SetFont(UIStyles.get_font('small'))
        self.delete_btn.Enable(False)
        button_sizer.Add(self.delete_btn, 0)
        
        list_sizer.Add(button_sizer, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.list_panel.SetSizer(list_sizer)
    
    def create_category_editor(self, parent) -> None:
        """创建分类编辑面板"""
        self.editor_panel = wx.Panel(parent)
        self.editor_panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.theme))
        editor_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 编辑器标题
        editor_title = wx.StaticText(self.editor_panel, label="分类详情")
        editor_title.SetFont(UIStyles.get_font('medium', bold=True))
        editor_sizer.Add(editor_title, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        # 分类名称
        name_label = wx.StaticText(self.editor_panel, label="分类名称:")
        name_label.SetFont(UIStyles.get_font('medium'))
        editor_sizer.Add(name_label, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.name_ctrl = wx.TextCtrl(self.editor_panel, size=(-1, 35))
        self.name_ctrl.SetFont(UIStyles.get_font('medium'))
        self.name_ctrl.Enable(False)
        editor_sizer.Add(self.name_ctrl, 0, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, UIStyles.SPACING['sm'])
        
        # 分类颜色
        color_label = wx.StaticText(self.editor_panel, label="分类颜色:")
        color_label.SetFont(UIStyles.get_font('medium'))
        editor_sizer.Add(color_label, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.color_picker = csel.ColourSelect(self.editor_panel, colour=wx.Colour("#3498db"), size=(100, 35))
        self.color_picker.Enable(False)
        editor_sizer.Add(self.color_picker, 0, wx.LEFT | wx.RIGHT | wx.BOTTOM, UIStyles.SPACING['sm'])
        
        # 分类图标
        icon_label = wx.StaticText(self.editor_panel, label="分类图标:")
        icon_label.SetFont(UIStyles.get_font('medium'))
        editor_sizer.Add(icon_label, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        # 图标选择网格 - 增加高度以显示更多图标
        self.icon_panel = wx.Panel(self.editor_panel, size=(-1, 200))
        self.icon_panel.SetBackgroundColour(UIStyles.get_color('bg_card', self.theme))
        self.create_icon_grid()
        editor_sizer.Add(self.icon_panel, 0, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, UIStyles.SPACING['sm'])
        
        # 预览区域
        preview_label = wx.StaticText(self.editor_panel, label="预览:")
        preview_label.SetFont(UIStyles.get_font('medium'))
        editor_sizer.Add(preview_label, 0, wx.ALL, UIStyles.SPACING['sm'])

        self.preview_panel = wx.Panel(self.editor_panel, size=(-1, 60))
        self.preview_panel.SetBackgroundColour(UIStyles.get_color('bg_card', self.theme))
        self.preview_panel.Bind(wx.EVT_PAINT, self.on_preview_paint)
        editor_sizer.Add(self.preview_panel, 0, wx.EXPAND | wx.LEFT | wx.RIGHT | wx.BOTTOM, UIStyles.SPACING['sm'])
        
        # 保存按钮
        self.save_btn = wx.Button(self.editor_panel, label="保存更改", size=(120, 35))
        self.save_btn.SetFont(UIStyles.get_font('medium', bold=True))
        self.save_btn.Enable(False)
        editor_sizer.Add(self.save_btn, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        self.editor_panel.SetSizer(editor_sizer)
    
    def create_icon_grid(self) -> None:
        """创建图标选择网格"""
        icon_sizer = wx.GridSizer(4, 4, 5, 5)
        
        self.icon_buttons = []
        for icon in self.available_icons:
            btn = wx.Button(self.icon_panel, label=icon, size=(40, 40))
            btn.SetFont(wx.Font(16, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
            btn.Enable(False)
            btn.Bind(wx.EVT_BUTTON, lambda evt, i=icon: self.on_icon_select(evt, i))
            self.icon_buttons.append(btn)
            icon_sizer.Add(btn, 0, wx.EXPAND)
        
        self.icon_panel.SetSizer(icon_sizer)
        
        # 当前选中的图标
        self.selected_icon = "💼"
    
    def create_button_panel(self, parent) -> wx.Panel:
        """创建按钮面板"""
        button_panel = wx.Panel(parent)
        button_panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.theme))
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # 添加弹性空间
        button_sizer.AddStretchSpacer()
        
        # 取消按钮
        cancel_btn = wx.Button(button_panel, wx.ID_CANCEL, "取消", size=(100, 35))
        cancel_btn.SetFont(UIStyles.get_font('medium'))
        button_sizer.Add(cancel_btn, 0, wx.RIGHT, UIStyles.SPACING['sm'])
        
        # 确定按钮
        ok_btn = wx.Button(button_panel, wx.ID_OK, "确定", size=(100, 35))
        ok_btn.SetFont(UIStyles.get_font('medium', bold=True))
        ok_btn.SetDefault()
        button_sizer.Add(ok_btn, 0)
        
        button_panel.SetSizer(button_sizer)
        return button_panel
    
    def setup_events(self) -> None:
        """设置事件处理"""
        # 列表选择事件
        self.category_listbox.Bind(wx.EVT_LISTBOX, self.on_category_select)
        
        # 按钮事件
        self.add_btn.Bind(wx.EVT_BUTTON, self.on_add_category)
        self.delete_btn.Bind(wx.EVT_BUTTON, self.on_delete_category)
        self.save_btn.Bind(wx.EVT_BUTTON, self.on_save_category)
        
        # 文本变化事件
        self.name_ctrl.Bind(wx.EVT_TEXT, self.on_text_change)
        
        # 颜色变化事件
        self.color_picker.Bind(csel.EVT_COLOURSELECT, self.on_color_change)
        
        # 对话框按钮事件
        self.Bind(wx.EVT_BUTTON, self.on_ok, id=wx.ID_OK)
        self.Bind(wx.EVT_BUTTON, self.on_cancel, id=wx.ID_CANCEL)
    
    def load_categories(self) -> None:
        """加载分类列表"""
        self.category_listbox.Clear()
        for category in self.categories:
            display_text = f"{category.icon} {category.name}" if category.icon else category.name
            self.category_listbox.Append(display_text)
    
    def on_category_select(self, event) -> None:
        """分类选择事件"""
        selection = self.category_listbox.GetSelection()
        if selection != wx.NOT_FOUND:
            category = self.categories[selection]
            self.load_category_details(category)
            self.enable_editor(True)
            self.delete_btn.Enable(True)
    
    def load_category_details(self, category: Category) -> None:
        """加载分类详情"""
        self.name_ctrl.SetValue(category.name)
        self.color_picker.SetColour(wx.Colour(category.color))
        self.selected_icon = category.icon or "💼"
        self.update_icon_selection()
        self.preview_panel.Refresh()
    
    def enable_editor(self, enable: bool) -> None:
        """启用/禁用编辑器"""
        self.name_ctrl.Enable(enable)
        self.color_picker.Enable(enable)
        for btn in self.icon_buttons:
            btn.Enable(enable)
        self.save_btn.Enable(enable)
    
    def update_icon_selection(self) -> None:
        """更新图标选择状态"""
        for btn in self.icon_buttons:
            if btn.GetLabel() == self.selected_icon:
                btn.SetBackgroundColour(UIStyles.get_color('primary', self.theme))
            else:
                btn.SetBackgroundColour(wx.NullColour)
    
    def on_add_category(self, event) -> None:
        """添加分类事件"""
        # 创建新分类（临时ID为负数，表示新增）
        temp_id = -(len(self.new_categories) + 1)  # 使用负数作为临时ID
        new_category = Category(
            id=temp_id,
            name="新分类",
            color="#3498db",
            icon="💼"
        )

        self.categories.append(new_category)
        self.new_categories.append(new_category)  # 添加到新增列表
        self.load_categories()

        # 选中新添加的分类
        self.category_listbox.SetSelection(len(self.categories) - 1)
        self.load_category_details(new_category)
        self.enable_editor(True)
        self.delete_btn.Enable(True)

        # 聚焦到名称输入框
        self.name_ctrl.SetFocus()
        self.name_ctrl.SelectAll()
    
    def on_delete_category(self, event) -> None:
        """删除分类事件"""
        selection = self.category_listbox.GetSelection()
        if selection != wx.NOT_FOUND:
            category = self.categories[selection]
            
            dlg = wx.MessageDialog(
                self,
                f"确定要删除分类 '{category.name}' 吗？\n删除后该分类下的任务将变为无分类。",
                "确认删除",
                wx.YES_NO | wx.ICON_QUESTION
            )
            
            if dlg.ShowModal() == wx.ID_YES:
                # 添加到删除列表
                if category.id:
                    self.deleted_categories.append(category)
                
                # 从列表中移除
                self.categories.pop(selection)
                self.load_categories()
                
                # 清空编辑器
                self.clear_editor()
                self.enable_editor(False)
                self.delete_btn.Enable(False)
            
            dlg.Destroy()
    
    def on_save_category(self, event) -> None:
        """保存分类事件"""
        selection = self.category_listbox.GetSelection()
        if selection != wx.NOT_FOUND:
            category = self.categories[selection]

            # 验证输入
            name = self.name_ctrl.GetValue().strip()
            if not name:
                wx.MessageBox("请输入分类名称", "输入错误", wx.OK | wx.ICON_WARNING)
                return

            # 检查重名
            for i, cat in enumerate(self.categories):
                if i != selection and cat.name == name:
                    wx.MessageBox("分类名称已存在", "输入错误", wx.OK | wx.ICON_WARNING)
                    return

            # 更新分类信息
            old_name = category.name
            category.name = name
            category.color = self.color_picker.GetColour().GetAsString(wx.C2S_HTML_SYNTAX)
            category.icon = self.selected_icon

            # 立即保存到数据库
            try:
                if category.id and category.id > 0:
                    # 现有分类，立即更新到数据库
                    if hasattr(self.parent_window, 'category_manager'):
                        success = self.parent_window.category_manager.update_category(
                            category.id, category.name, category.color, category.icon
                        )
                        if success:
                            # 添加到修改列表（用于对话框关闭时的统计）
                            if category not in self.modified_categories:
                                self.modified_categories.append(category)

                            # 刷新父窗口的分类显示
                            if hasattr(self.parent_window, 'refresh_categories'):
                                self.parent_window.refresh_categories()

                            wx.MessageBox(f"分类 '{category.name}' 保存成功！", "保存成功", wx.OK | wx.ICON_INFORMATION)
                            print(f"成功保存分类: {old_name} -> {category.name}")
                        else:
                            wx.MessageBox("保存失败，请重试。", "保存失败", wx.OK | wx.ICON_ERROR)
                            print(f"保存分类失败: {category.name}")
                    else:
                        wx.MessageBox("无法访问分类管理器，请重试。", "保存失败", wx.OK | wx.ICON_ERROR)
                else:
                    # 新分类，立即添加到数据库
                    if hasattr(self.parent_window, 'category_manager'):
                        new_id = self.parent_window.category_manager.add_category(
                            category.name, category.color, category.icon
                        )
                        if new_id > 0:
                            # 更新分类ID
                            category.id = new_id

                            # 从新增列表移除，添加到修改列表
                            if category in self.new_categories:
                                self.new_categories.remove(category)
                            if category not in self.modified_categories:
                                self.modified_categories.append(category)

                            # 刷新父窗口的分类显示
                            if hasattr(self.parent_window, 'refresh_categories'):
                                self.parent_window.refresh_categories()

                            wx.MessageBox(f"新分类 '{category.name}' 创建成功！", "创建成功", wx.OK | wx.ICON_INFORMATION)
                            print(f"成功创建新分类: {category.name} (ID: {new_id})")
                        else:
                            wx.MessageBox("创建失败，请重试。", "创建失败", wx.OK | wx.ICON_ERROR)
                            print(f"创建新分类失败: {category.name}")
                    else:
                        wx.MessageBox("无法访问分类管理器，请重试。", "创建失败", wx.OK | wx.ICON_ERROR)

            except Exception as e:
                wx.MessageBox(f"保存时发生错误: {str(e)}", "保存失败", wx.OK | wx.ICON_ERROR)
                print(f"保存分类异常: {category.name} - {e}")

            # 刷新列表显示
            self.load_categories()
            self.category_listbox.SetSelection(selection)
    
    def on_icon_select(self, event, icon: str) -> None:
        """图标选择事件"""
        self.selected_icon = icon
        self.update_icon_selection()
        self.preview_panel.Refresh()
    
    def on_text_change(self, event) -> None:
        """文本变化事件"""
        self.preview_panel.Refresh()
    
    def on_color_change(self, event) -> None:
        """颜色变化事件"""
        self.preview_panel.Refresh()
    
    def on_preview_paint(self, event) -> None:
        """预览面板绘制事件"""
        dc = wx.PaintDC(self.preview_panel)
        size = self.preview_panel.GetSize()
        
        # 清空背景
        dc.SetBackground(wx.Brush(UIStyles.get_color('bg_card', self.theme)))
        dc.Clear()
        
        # 绘制预览
        if self.name_ctrl.IsEnabled():
            name = self.name_ctrl.GetValue() or "分类名称"
            color = self.color_picker.GetColour()
            
            # 绘制颜色条
            dc.SetBrush(wx.Brush(color))
            dc.SetPen(wx.Pen(color))
            dc.DrawRectangle(10, 10, 4, size.height - 20)
            
            # 绘制图标和文本
            dc.SetTextForeground(UIStyles.get_color('text_primary', self.theme))
            dc.SetFont(UIStyles.get_font('medium'))
            
            text = f"{self.selected_icon} {name}"
            dc.DrawText(text, 25, (size.height - dc.GetTextExtent(text)[1]) // 2)
    
    def clear_editor(self) -> None:
        """清空编辑器"""
        self.name_ctrl.SetValue("")
        self.color_picker.SetColour(wx.Colour("#3498db"))
        self.selected_icon = "💼"
        self.update_icon_selection()
        self.preview_panel.Refresh()
    
    def on_ok(self, event) -> None:
        """确定按钮事件"""
        self.EndModal(wx.ID_OK)
    
    def on_cancel(self, event) -> None:
        """取消按钮事件"""
        self.EndModal(wx.ID_CANCEL)
    
    def get_modified_categories(self) -> List[Category]:
        """获取修改的分类"""
        return self.modified_categories

    def get_deleted_categories(self) -> List[Category]:
        """获取删除的分类"""
        return self.deleted_categories

    def get_new_categories(self) -> List[Category]:
        """获取新增的分类"""
        return self.new_categories

    def get_all_categories(self) -> List[Category]:
        """获取所有分类"""
        return self.categories
