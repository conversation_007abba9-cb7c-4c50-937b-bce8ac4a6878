"""
菜单修复测试程序
"""
import wx
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class MenuTestWindow(wx.Frame):
    """菜单测试窗口"""
    
    def __init__(self):
        super().__init__(None, title="菜单事件绑定测试", size=(600, 400))
        
        self.init_ui()
        self.Center()
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建菜单栏
        menubar = wx.MenuBar()
        
        # 文件菜单
        file_menu = wx.Menu()
        file_menu.Append(wx.ID_NEW, "新建\tCtrl+N")
        file_menu.Append(wx.ID_EXIT, "退出\tCtrl+Q")
        menubar.Append(file_menu, "文件")
        
        # 视图菜单
        view_menu = wx.Menu()
        self.floating_menu_item = view_menu.AppendCheckItem(wx.ID_ANY, "悬浮模式\tF11")
        view_menu.AppendSeparator()
        
        # 主题子菜单
        theme_submenu = wx.Menu()
        self.light_theme_item = theme_submenu.AppendRadioItem(wx.ID_ANY, "明亮主题")
        self.dark_theme_item = theme_submenu.AppendRadioItem(wx.ID_ANY, "暗黑主题")
        view_menu.AppendSubMenu(theme_submenu, "主题")
        
        menubar.Append(view_menu, "视图")
        
        self.SetMenuBar(menubar)
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建主面板
        panel = wx.Panel(self)
        panel.SetBackgroundColour(wx.Colour("#f8f9fa"))
        
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题
        title = wx.StaticText(panel, label="菜单事件绑定测试")
        title.SetFont(wx.Font(16, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        sizer.Add(title, 0, wx.ALL | wx.CENTER, 20)
        
        # 状态显示
        self.status_text = wx.TextCtrl(panel, style=wx.TE_MULTILINE | wx.TE_READONLY, size=(-1, 200))
        self.status_text.SetFont(wx.Font(10, wx.FONTFAMILY_TELETYPE, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        sizer.Add(self.status_text, 1, wx.EXPAND | wx.ALL, 20)
        
        # 测试按钮
        test_btn = wx.Button(panel, label="测试所有菜单功能")
        test_btn.Bind(wx.EVT_BUTTON, self.test_all_menus)
        sizer.Add(test_btn, 0, wx.CENTER | wx.ALL, 10)
        
        panel.SetSizer(sizer)
        
        # 绑定事件
        self.setup_events()
        
        # 初始状态
        self.light_theme_item.Check(True)
        self.log_status("应用程序已启动，菜单事件绑定完成")
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = self.CreateToolBar(wx.TB_HORIZONTAL | wx.TB_FLAT)
        
        # 添加工具按钮
        toolbar.AddTool(wx.ID_NEW, "新建", wx.ArtProvider.GetBitmap(wx.ART_NEW), "新建任务")
        toolbar.AddSeparator()
        toolbar.AddTool(wx.ID_REFRESH, "刷新", wx.ArtProvider.GetBitmap(wx.ART_REDO), "刷新列表")
        toolbar.AddSeparator()
        
        # 搜索框
        search_label = wx.StaticText(toolbar, label="搜索:")
        toolbar.AddControl(search_label)
        
        self.search_ctrl = wx.SearchCtrl(toolbar, size=(200, -1))
        toolbar.AddControl(self.search_ctrl)
        
        toolbar.Realize()
    
    def setup_events(self):
        """设置事件处理"""
        # 菜单事件 - 正确的绑定方式
        self.Bind(wx.EVT_MENU, self.on_new, id=wx.ID_NEW)
        self.Bind(wx.EVT_MENU, self.on_exit, id=wx.ID_EXIT)
        self.Bind(wx.EVT_MENU, self.on_toggle_floating_mode, self.floating_menu_item)
        self.Bind(wx.EVT_MENU, self.on_light_theme, self.light_theme_item)
        self.Bind(wx.EVT_MENU, self.on_dark_theme, self.dark_theme_item)
        
        # 工具栏事件
        self.Bind(wx.EVT_TOOL, self.on_new, id=wx.ID_NEW)
        self.Bind(wx.EVT_TOOL, self.on_refresh, id=wx.ID_REFRESH)
        
        # 搜索事件
        self.search_ctrl.Bind(wx.EVT_TEXT, self.on_search)
        
        # 窗口事件
        self.Bind(wx.EVT_CLOSE, self.on_close)
    
    def log_status(self, message):
        """记录状态信息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.status_text.AppendText(log_message)
    
    def on_new(self, event):
        """新建事件"""
        self.log_status("✅ 新建菜单/工具栏事件触发")
    
    def on_exit(self, event):
        """退出事件"""
        self.log_status("✅ 退出菜单事件触发")
        self.Close()
    
    def on_refresh(self, event):
        """刷新事件"""
        self.log_status("✅ 刷新工具栏事件触发")
    
    def on_toggle_floating_mode(self, event):
        """切换悬浮模式事件"""
        is_checked = self.floating_menu_item.IsChecked()
        self.log_status(f"✅ 悬浮模式切换事件触发 - 状态: {'开启' if is_checked else '关闭'}")
    
    def on_light_theme(self, event):
        """明亮主题事件"""
        self.log_status("✅ 明亮主题选择事件触发")
        self.apply_theme('light')
    
    def on_dark_theme(self, event):
        """暗黑主题事件"""
        self.log_status("✅ 暗黑主题选择事件触发")
        self.apply_theme('dark')
    
    def on_search(self, event):
        """搜索事件"""
        search_text = self.search_ctrl.GetValue()
        self.log_status(f"✅ 搜索事件触发 - 搜索内容: '{search_text}'")
    
    def on_close(self, event):
        """关闭事件"""
        self.log_status("✅ 窗口关闭事件触发")
        self.Destroy()
    
    def apply_theme(self, theme):
        """应用主题"""
        if theme == 'dark':
            self.SetBackgroundColour(wx.Colour("#2c3e50"))
            for child in self.GetChildren():
                if isinstance(child, wx.Panel):
                    child.SetBackgroundColour(wx.Colour("#34495e"))
        else:
            self.SetBackgroundColour(wx.Colour("#ffffff"))
            for child in self.GetChildren():
                if isinstance(child, wx.Panel):
                    child.SetBackgroundColour(wx.Colour("#f8f9fa"))
        
        self.Refresh()
        self.log_status(f"✅ 主题已切换到: {theme}")
    
    def test_all_menus(self, event):
        """测试所有菜单功能"""
        self.log_status("🧪 开始测试所有菜单功能...")
        
        # 测试悬浮模式切换
        self.floating_menu_item.Check(True)
        self.on_toggle_floating_mode(None)
        
        wx.CallLater(500, self._continue_test_1)
    
    def _continue_test_1(self):
        """继续测试1"""
        self.floating_menu_item.Check(False)
        self.on_toggle_floating_mode(None)
        
        wx.CallLater(500, self._continue_test_2)
    
    def _continue_test_2(self):
        """继续测试2"""
        # 测试主题切换
        self.dark_theme_item.Check(True)
        self.on_dark_theme(None)
        
        wx.CallLater(1000, self._continue_test_3)
    
    def _continue_test_3(self):
        """继续测试3"""
        self.light_theme_item.Check(True)
        self.on_light_theme(None)
        
        self.log_status("🎉 所有菜单功能测试完成！")


class MenuTestApp(wx.App):
    """菜单测试应用"""
    
    def OnInit(self):
        try:
            self.main_window = MenuTestWindow()
            self.main_window.Show()
            self.SetTopWindow(self.main_window)
            return True
        except Exception as e:
            wx.MessageBox(f"应用启动失败: {str(e)}", "错误", wx.OK | wx.ICON_ERROR)
            return False


def main():
    """主函数"""
    app = MenuTestApp()
    app.MainLoop()


if __name__ == "__main__":
    main()
