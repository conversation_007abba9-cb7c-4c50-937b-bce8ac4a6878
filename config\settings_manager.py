"""
设置管理器
"""
import json
import os
from typing import Dict, Any, Optional
from datetime import datetime


class SettingsManager:
    """设置管理类"""
    
    def __init__(self, settings_file: str = "settings.json"):
        """初始化设置管理器"""
        self.settings_file = settings_file
        self.settings = {}
        self.default_settings = self._get_default_settings()
        
        # 加载设置
        self.load_settings()
    
    def _get_default_settings(self) -> Dict[str, Any]:
        """获取默认设置"""
        return {
            # 常规设置
            'auto_start': False,
            'minimize_to_tray': False,
            'remember_window': True,
            'always_on_top': False,
            'auto_backup': True,
            
            # 窗口设置
            'window_width': 1000,
            'window_height': 700,
            'window_x': -1,  # -1表示居中
            'window_y': -1,  # -1表示居中
            'window_maximized': False,
            'sidebar_width': 250,
            
            # 外观设置
            'theme': 'light',
            'font_size': 'normal',
            'enable_animations': True,
            'animation_speed': 50,
            
            # 通知设置
            'notifications_enabled': True,
            'sound_enabled': True,
            'reminder_time': 15,
            'daily_summary': True,
            'summary_time': '09:00',
            
            # 高级设置
            'global_hotkey': False,
            'hotkey': 'Ctrl+Alt+T',
            'hardware_acceleration': True,
            'reduce_animations': False,
            'debug_mode': False,
            'log_level': 'info',
            
            # 数据设置
            'backup_interval': 24,  # 小时
            'max_backups': 10,
            'auto_cleanup': True,
            
            # 任务设置
            'default_priority': 1,
            'auto_complete_subtasks': False,
            'show_completed_tasks': True,
            'task_sort_order': 'priority',  # priority, date, name
            
            # 界面设置
            'show_sidebar': True,
            'show_toolbar': True,
            'show_statusbar': True,
            'compact_mode': False,
            
            # 最后更新时间
            'last_updated': datetime.now().isoformat()
        }
    
    def load_settings(self) -> None:
        """加载设置"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                
                # 合并默认设置和加载的设置
                self.settings = self.default_settings.copy()
                self.settings.update(loaded_settings)
                
                # 验证设置
                self._validate_settings()
            else:
                # 使用默认设置
                self.settings = self.default_settings.copy()
                self.save_settings()
                
        except Exception as e:
            print(f"加载设置失败: {e}")
            # 使用默认设置
            self.settings = self.default_settings.copy()
    
    def save_settings(self) -> bool:
        """保存设置"""
        try:
            # 更新最后修改时间
            self.settings['last_updated'] = datetime.now().isoformat()
            
            # 创建目录（如果不存在）
            os.makedirs(os.path.dirname(self.settings_file) if os.path.dirname(self.settings_file) else '.', exist_ok=True)
            
            # 保存到文件
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"保存设置失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取设置值"""
        return self.settings.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """设置值"""
        self.settings[key] = value
    
    def update(self, new_settings: Dict[str, Any]) -> None:
        """批量更新设置"""
        self.settings.update(new_settings)
    
    def reset_to_defaults(self) -> None:
        """重置为默认设置"""
        self.settings = self.default_settings.copy()
    
    def get_all_settings(self) -> Dict[str, Any]:
        """获取所有设置"""
        return self.settings.copy()
    
    def _validate_settings(self) -> None:
        """验证设置有效性"""
        # 验证主题
        if self.settings.get('theme') not in ['light', 'dark', 'auto']:
            self.settings['theme'] = 'light'
        
        # 验证字体大小
        if self.settings.get('font_size') not in ['small', 'normal', 'large', 'xlarge']:
            self.settings['font_size'] = 'normal'
        
        # 验证日志级别
        if self.settings.get('log_level') not in ['error', 'warning', 'info', 'debug']:
            self.settings['log_level'] = 'info'
        
        # 验证数值范围
        if not (10 <= self.settings.get('animation_speed', 50) <= 100):
            self.settings['animation_speed'] = 50
        
        if not (1 <= self.settings.get('reminder_time', 15) <= 1440):
            self.settings['reminder_time'] = 15
        
        if not (600 <= self.settings.get('window_width', 1000) <= 3840):
            self.settings['window_width'] = 1000
        
        if not (400 <= self.settings.get('window_height', 700) <= 2160):
            self.settings['window_height'] = 700
        
        if not (200 <= self.settings.get('sidebar_width', 250) <= 500):
            self.settings['sidebar_width'] = 250
    
    def export_settings(self, export_path: str) -> bool:
        """导出设置"""
        try:
            export_data = {
                'version': '1.0',
                'export_date': datetime.now().isoformat(),
                'settings': self.settings
            }
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"导出设置失败: {e}")
            return False
    
    def import_settings(self, import_path: str) -> bool:
        """导入设置"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            # 验证导入数据格式
            if 'settings' not in import_data:
                print("无效的设置文件格式")
                return False
            
            # 导入设置
            imported_settings = import_data['settings']
            
            # 只导入有效的设置项
            for key, value in imported_settings.items():
                if key in self.default_settings:
                    self.settings[key] = value
            
            # 验证设置
            self._validate_settings()
            
            # 保存设置
            return self.save_settings()
            
        except Exception as e:
            print(f"导入设置失败: {e}")
            return False
    
    def backup_settings(self, backup_path: Optional[str] = None) -> bool:
        """备份设置"""
        if backup_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"settings_backup_{timestamp}.json"
        
        return self.export_settings(backup_path)
    
    def get_window_geometry(self) -> Dict[str, int]:
        """获取窗口几何信息"""
        return {
            'width': self.get('window_width', 1000),
            'height': self.get('window_height', 700),
            'x': self.get('window_x', -1),
            'y': self.get('window_y', -1),
            'maximized': self.get('window_maximized', False)
        }
    
    def set_window_geometry(self, width: int, height: int, x: int, y: int, maximized: bool = False) -> None:
        """设置窗口几何信息"""
        self.set('window_width', width)
        self.set('window_height', height)
        self.set('window_x', x)
        self.set('window_y', y)
        self.set('window_maximized', maximized)
    
    def is_first_run(self) -> bool:
        """检查是否首次运行"""
        return not os.path.exists(self.settings_file)
    
    def get_theme_settings(self) -> Dict[str, Any]:
        """获取主题相关设置"""
        return {
            'theme': self.get('theme', 'light'),
            'font_size': self.get('font_size', 'normal'),
            'enable_animations': self.get('enable_animations', True),
            'animation_speed': self.get('animation_speed', 50),
            'compact_mode': self.get('compact_mode', False)
        }
    
    def get_notification_settings(self) -> Dict[str, Any]:
        """获取通知相关设置"""
        return {
            'notifications_enabled': self.get('notifications_enabled', True),
            'sound_enabled': self.get('sound_enabled', True),
            'reminder_time': self.get('reminder_time', 15),
            'daily_summary': self.get('daily_summary', True),
            'summary_time': self.get('summary_time', '09:00')
        }
