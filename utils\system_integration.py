"""
系统集成功能
"""
import os
import sys
import winreg
import threading
from typing import Optional, Callable
import pystray
from PIL import Image, ImageDraw
import wx


class SystemIntegration:
    """系统集成类"""
    
    def __init__(self, app_name: str = "Todo List"):
        """初始化系统集成"""
        self.app_name = app_name
        self.tray_icon = None
        self.main_window = None
        self.is_minimized_to_tray = False
    
    def setup_autostart(self, enable: bool = True) -> bool:
        """设置开机自启"""
        try:
            # 获取当前程序路径
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe文件
                app_path = sys.executable
            else:
                # 如果是Python脚本
                app_path = f'"{sys.executable}" "{os.path.abspath(sys.argv[0])}"'
            
            # 注册表路径
            reg_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
            
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path, 0, winreg.KEY_SET_VALUE) as key:
                if enable:
                    # 添加到开机启动
                    winreg.SetValueEx(key, self.app_name, 0, winreg.REG_SZ, app_path)
                else:
                    # 从开机启动中移除
                    try:
                        winreg.DeleteValue(key, self.app_name)
                    except FileNotFoundError:
                        pass  # 如果不存在则忽略
            
            return True
            
        except Exception as e:
            print(f"设置开机自启失败: {e}")
            return False
    
    def is_autostart_enabled(self) -> bool:
        """检查是否已设置开机自启"""
        try:
            reg_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path, 0, winreg.KEY_READ) as key:
                try:
                    winreg.QueryValueEx(key, self.app_name)
                    return True
                except FileNotFoundError:
                    return False
        except Exception:
            return False
    
    def create_tray_icon(self, main_window, icon_path: Optional[str] = None) -> None:
        """创建系统托盘图标"""
        self.main_window = main_window
        
        # 创建托盘图标
        if icon_path and os.path.exists(icon_path):
            image = Image.open(icon_path)
        else:
            # 创建默认图标
            image = self._create_default_icon()
        
        # 创建托盘菜单
        menu = pystray.Menu(
            pystray.MenuItem("显示", self.show_window),
            pystray.MenuItem("隐藏", self.hide_window),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("新建任务", self.new_todo),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("退出", self.quit_app)
        )
        
        # 创建托盘图标
        self.tray_icon = pystray.Icon(
            name=self.app_name,
            icon=image,
            title=self.app_name,
            menu=menu
        )
        
        # 在单独线程中运行托盘图标
        self.tray_thread = threading.Thread(target=self.tray_icon.run, daemon=True)
        self.tray_thread.start()
    
    def _create_default_icon(self) -> Image.Image:
        """创建默认托盘图标"""
        # 创建16x16的图标
        image = Image.new('RGBA', (16, 16), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        # 绘制简单的方形图标
        draw.rectangle([2, 2, 14, 14], fill=(52, 152, 219, 255), outline=(41, 128, 185, 255))
        draw.rectangle([4, 4, 12, 12], fill=(255, 255, 255, 255))
        
        return image
    
    def show_window(self, icon=None, item=None) -> None:
        """显示主窗口"""
        if self.main_window:
            wx.CallAfter(self._show_window_safe)
    
    def _show_window_safe(self) -> None:
        """安全地显示窗口（在主线程中）"""
        if self.main_window:
            self.main_window.Show()
            self.main_window.Restore()
            self.main_window.Raise()
            self.is_minimized_to_tray = False
    
    def hide_window(self, icon=None, item=None) -> None:
        """隐藏主窗口"""
        if self.main_window:
            wx.CallAfter(self._hide_window_safe)
    
    def _hide_window_safe(self) -> None:
        """安全地隐藏窗口（在主线程中）"""
        if self.main_window:
            self.main_window.Hide()
            self.is_minimized_to_tray = True
    
    def minimize_to_tray(self) -> None:
        """最小化到托盘"""
        self.hide_window()
    
    def restore_from_tray(self) -> None:
        """从托盘恢复"""
        self.show_window()
    
    def new_todo(self, icon=None, item=None) -> None:
        """新建任务（从托盘）"""
        if self.main_window:
            wx.CallAfter(self._new_todo_safe)
    
    def _new_todo_safe(self) -> None:
        """安全地新建任务（在主线程中）"""
        if self.main_window:
            # 显示窗口
            self.show_window()
            # 触发新建任务事件
            if hasattr(self.main_window, 'on_new_todo'):
                event = wx.CommandEvent(wx.wxEVT_COMMAND_MENU_SELECTED, wx.ID_NEW)
                self.main_window.on_new_todo(event)
    
    def quit_app(self, icon=None, item=None) -> None:
        """退出应用程序"""
        if self.tray_icon:
            self.tray_icon.stop()
        
        if self.main_window:
            wx.CallAfter(self._quit_app_safe)
    
    def _quit_app_safe(self) -> None:
        """安全地退出应用程序（在主线程中）"""
        if self.main_window:
            self.main_window.Close()
    
    def setup_global_hotkeys(self) -> None:
        """设置全局快捷键"""
        # TODO: 实现全局快捷键功能
        # 可以使用keyboard库或win32api实现
        pass
    
    def show_notification(self, title: str, message: str, timeout: int = 5) -> None:
        """显示系统通知"""
        if self.tray_icon:
            self.tray_icon.notify(title=title, message=message, timeout=timeout)
    
    def cleanup(self) -> None:
        """清理资源"""
        if self.tray_icon:
            self.tray_icon.stop()
    
    def set_tray_tooltip(self, tooltip: str) -> None:
        """设置托盘图标提示文本"""
        if self.tray_icon:
            self.tray_icon.title = tooltip
    
    def update_tray_icon(self, icon_path: str) -> None:
        """更新托盘图标"""
        if self.tray_icon and os.path.exists(icon_path):
            try:
                image = Image.open(icon_path)
                self.tray_icon.icon = image
            except Exception as e:
                print(f"更新托盘图标失败: {e}")


class FloatingWindow(wx.Frame):
    """悬浮窗类"""

    def __init__(self, parent, todo_manager, theme='light'):
        """初始化悬浮窗"""
        super().__init__(parent, title="Todo List - 悬浮窗",
                        style=wx.STAY_ON_TOP | wx.FRAME_TOOL_WINDOW | wx.FRAME_NO_TASKBAR)

        self.todo_manager = todo_manager
        self.theme = theme
        self.parent_window = parent
        self.SetSize((350, 450))

        # 初始化UI
        self.init_ui()

        # 设置半透明
        self.SetTransparent(240)

        # 居中显示
        self.Center()
    
    def init_ui(self) -> None:
        """初始化用户界面"""
        # 导入UIStyles
        try:
            from ui.styles import UIStyles
        except ImportError:
            UIStyles = None

        panel = wx.Panel(self)
        if UIStyles:
            panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.theme))
        else:
            panel.SetBackgroundColour(wx.Colour(240, 240, 240))

        sizer = wx.BoxSizer(wx.VERTICAL)

        # 标题栏
        title_panel = wx.Panel(panel)
        if UIStyles:
            title_panel.SetBackgroundColour(UIStyles.get_color('bg_secondary', self.theme))
        title_sizer = wx.BoxSizer(wx.HORIZONTAL)

        # 标题
        title = wx.StaticText(title_panel, label="📋 快速任务")
        title.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        if UIStyles:
            title.SetForegroundColour(UIStyles.get_color('text_primary', self.theme))
        title_sizer.Add(title, 1, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 8)

        # 关闭按钮
        close_btn = wx.Button(title_panel, label="×", size=(25, 25))
        close_btn.SetFont(wx.Font(12, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        close_btn.Bind(wx.EVT_BUTTON, self.on_close)
        title_sizer.Add(close_btn, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, 5)

        title_panel.SetSizer(title_sizer)
        sizer.Add(title_panel, 0, wx.EXPAND)

        # 任务列表
        self.task_list = wx.ListCtrl(panel, style=wx.LC_REPORT | wx.LC_SINGLE_SEL | wx.LC_NO_HEADER)
        self.task_list.AppendColumn("任务", width=300)
        if UIStyles:
            self.task_list.SetBackgroundColour(UIStyles.get_color('bg_card', self.theme))
            self.task_list.SetForegroundColour(UIStyles.get_color('text_primary', self.theme))
        sizer.Add(self.task_list, 1, wx.EXPAND | wx.ALL, 10)

        # 按钮面板
        btn_panel = wx.Panel(panel)
        if UIStyles:
            btn_panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.theme))
        btn_sizer = wx.BoxSizer(wx.HORIZONTAL)

        # 新建按钮
        new_btn = wx.Button(btn_panel, label="➕ 新建")
        new_btn.Bind(wx.EVT_BUTTON, self.on_new_task)
        btn_sizer.Add(new_btn, 1, wx.EXPAND | wx.RIGHT, 5)

        # 刷新按钮
        refresh_btn = wx.Button(btn_panel, label="🔄 刷新")
        refresh_btn.Bind(wx.EVT_BUTTON, self.on_refresh)
        btn_sizer.Add(refresh_btn, 1, wx.EXPAND | wx.LEFT, 5)

        btn_panel.SetSizer(btn_sizer)
        sizer.Add(btn_panel, 0, wx.EXPAND | wx.ALL, 10)

        panel.SetSizer(sizer)

        # 绑定事件
        self.task_list.Bind(wx.EVT_LIST_ITEM_ACTIVATED, self.on_task_double_click)
        self.Bind(wx.EVT_CLOSE, self.on_close)

        # 刷新任务列表
        self.refresh_tasks()
    
    def refresh_tasks(self) -> None:
        """刷新任务列表"""
        self.task_list.DeleteAllItems()

        try:
            # 获取未完成的任务
            todos = self.todo_manager.get_todos({'completed': False})

            for i, todo in enumerate(todos[:15]):  # 显示前15个任务
                # 创建显示文本
                display_text = todo.title
                if hasattr(todo, 'priority') and todo.priority:
                    priority_text = {1: "🔵", 2: "🟡", 3: "🔴"}.get(todo.priority, "")
                    display_text = f"{priority_text} {todo.title}"

                index = self.task_list.InsertItem(i, display_text)
                self.task_list.SetItemData(index, todo.id)

                # 设置颜色
                if hasattr(todo, 'is_overdue') and todo.is_overdue:
                    self.task_list.SetItemTextColour(index, wx.Colour(231, 76, 60))  # 红色
        except Exception as e:
            print(f"刷新悬浮窗任务列表失败: {e}")

    def on_new_task(self, event) -> None:
        """新建任务事件"""
        dlg = wx.TextEntryDialog(self, "请输入任务标题:", "新建任务")
        if dlg.ShowModal() == wx.ID_OK:
            title = dlg.GetValue().strip()
            if title:
                try:
                    self.todo_manager.add_todo(title)
                    self.refresh_tasks()
                    # 通知父窗口刷新
                    if hasattr(self.parent_window, 'refresh_todo_list'):
                        self.parent_window.refresh_todo_list()
                except Exception as e:
                    wx.MessageBox(f"创建任务失败: {e}", "错误", wx.OK | wx.ICON_ERROR)
        dlg.Destroy()

    def on_refresh(self, event) -> None:
        """刷新按钮事件"""
        self.refresh_tasks()

    def on_task_double_click(self, event) -> None:
        """任务双击事件"""
        selected = event.GetIndex()
        if selected != -1:
            todo_id = self.task_list.GetItemData(selected)
            try:
                # 切换任务完成状态
                self.todo_manager.mark_completed(todo_id, True)
                self.refresh_tasks()
                # 通知父窗口刷新
                if hasattr(self.parent_window, 'refresh_todo_list'):
                    self.parent_window.refresh_todo_list()
            except Exception as e:
                wx.MessageBox(f"更新任务失败: {e}", "错误", wx.OK | wx.ICON_ERROR)

    def on_close(self, event) -> None:
        """关闭事件"""
        # 通知父窗口悬浮窗已关闭
        if hasattr(self.parent_window, 'on_floating_window_closed'):
            self.parent_window.on_floating_window_closed()
        self.Destroy()
