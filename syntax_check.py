"""
语法检查脚本
"""
import ast
import os
import sys

def check_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 尝试解析AST
        ast.parse(source)
        return True, "语法正确"
    except SyntaxError as e:
        return False, f"语法错误: {e.msg} (行 {e.lineno})"
    except Exception as e:
        return False, f"检查失败: {str(e)}"

def main():
    """主函数"""
    files_to_check = [
        "main.py",
        "ui/main_window.py",
        "ui/components/category_manager_dialog.py",
        "core/folder_manager.py",
        "core/category_manager.py",
        "core/database.py"
    ]
    
    print("开始语法检查...")
    print("=" * 50)
    
    all_ok = True
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            is_ok, message = check_syntax(file_path)
            status = "✅" if is_ok else "❌"
            print(f"{status} {file_path}: {message}")
            if not is_ok:
                all_ok = False
        else:
            print(f"⚠️ {file_path}: 文件不存在")
    
    print("=" * 50)
    if all_ok:
        print("🎉 所有文件语法检查通过！")
    else:
        print("❌ 发现语法错误，请修复后重试。")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
