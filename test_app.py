"""
简化版测试应用
"""
import wx
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.styles import UIStyles
from ui.animations import AnimationManager


class SimpleMainWindow(wx.Frame):
    """简化版主窗口"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__(None, title="Todo List - 测试版", size=(800, 600))
        
        # 初始化组件
        self.animation_manager = AnimationManager()
        self.current_theme = 'light'
        
        # 初始化UI
        self.init_ui()
        self.setup_events()
        
        # 居中显示
        self.Center()
    
    def init_ui(self) -> None:
        """初始化用户界面"""
        # 创建主面板
        self.main_panel = wx.Panel(self)
        self.main_panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.current_theme))
        
        # 创建布局
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题
        title = wx.StaticText(self.main_panel, label="Todo List 现代化界面测试")
        title.SetFont(UIStyles.get_font('title', bold=True))
        title.SetForegroundColour(UIStyles.get_color('text_primary', self.current_theme))
        main_sizer.Add(title, 0, wx.ALL | wx.CENTER, UIStyles.SPACING['lg'])
        
        # 测试卡片区域
        self.create_test_cards(main_sizer)
        
        # 按钮区域
        self.create_buttons(main_sizer)
        
        self.main_panel.SetSizer(main_sizer)
    
    def create_test_cards(self, sizer) -> None:
        """创建测试卡片"""
        # 卡片容器
        card_panel = wx.Panel(self.main_panel)
        card_panel.SetBackgroundColour(UIStyles.get_color('bg_secondary', self.current_theme))
        card_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 创建几个测试卡片
        for i in range(3):
            card = self.create_card(card_panel, f"测试任务 {i+1}", f"这是第{i+1}个测试任务的描述", i+1)
            card_sizer.Add(card, 0, wx.EXPAND | wx.ALL, UIStyles.SPACING['sm'])
        
        card_panel.SetSizer(card_sizer)
        sizer.Add(card_panel, 1, wx.EXPAND | wx.ALL, UIStyles.SPACING['md'])
    
    def create_card(self, parent, title: str, description: str, priority: int) -> wx.Panel:
        """创建单个卡片"""
        card = wx.Panel(parent)
        card.SetBackgroundColour(UIStyles.get_color('bg_card', self.current_theme))
        card.SetMinSize((400, 80))
        
        # 卡片布局
        card_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # 优先级颜色条
        priority_colors = {1: '#95a5a6', 2: '#f39c12', 3: '#e74c3c'}
        priority_bar = wx.Panel(card, size=(4, -1))
        priority_bar.SetBackgroundColour(wx.Colour(priority_colors[priority]))
        card_sizer.Add(priority_bar, 0, wx.EXPAND | wx.RIGHT, UIStyles.SPACING['sm'])
        
        # 复选框
        checkbox = wx.CheckBox(card)
        card_sizer.Add(checkbox, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, UIStyles.SPACING['sm'])
        
        # 内容区域
        content_panel = wx.Panel(card)
        content_panel.SetBackgroundColour(UIStyles.get_color('bg_card', self.current_theme))
        content_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题
        title_label = wx.StaticText(content_panel, label=title)
        title_label.SetFont(UIStyles.get_font('medium', bold=True))
        title_label.SetForegroundColour(UIStyles.get_color('text_primary', self.current_theme))
        content_sizer.Add(title_label, 0, wx.EXPAND | wx.BOTTOM, UIStyles.SPACING['xs'])
        
        # 描述
        desc_label = wx.StaticText(content_panel, label=description)
        desc_label.SetFont(UIStyles.get_font('small'))
        desc_label.SetForegroundColour(UIStyles.get_color('text_secondary', self.current_theme))
        content_sizer.Add(desc_label, 0, wx.EXPAND)
        
        content_panel.SetSizer(content_sizer)
        card_sizer.Add(content_panel, 1, wx.EXPAND | wx.ALL, UIStyles.SPACING['sm'])
        
        # 按钮区域
        btn_panel = wx.Panel(card)
        btn_panel.SetBackgroundColour(UIStyles.get_color('bg_card', self.current_theme))
        btn_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        edit_btn = wx.Button(btn_panel, label="编辑", size=(60, 30))
        edit_btn.SetFont(UIStyles.get_font('small'))
        btn_sizer.Add(edit_btn, 0, wx.RIGHT, UIStyles.SPACING['xs'])
        
        delete_btn = wx.Button(btn_panel, label="删除", size=(60, 30))
        delete_btn.SetFont(UIStyles.get_font('small'))
        btn_sizer.Add(delete_btn, 0)
        
        btn_panel.SetSizer(btn_sizer)
        card_sizer.Add(btn_panel, 0, wx.ALIGN_CENTER_VERTICAL | wx.ALL, UIStyles.SPACING['sm'])
        
        card.SetSizer(card_sizer)
        
        # 绑定悬停事件
        card.Bind(wx.EVT_ENTER_WINDOW, lambda evt: self.on_card_hover(evt, card))
        card.Bind(wx.EVT_LEAVE_WINDOW, lambda evt: self.on_card_leave(evt, card))
        
        return card
    
    def create_buttons(self, sizer) -> None:
        """创建按钮区域"""
        btn_panel = wx.Panel(self.main_panel)
        btn_panel.SetBackgroundColour(UIStyles.get_color('bg_primary', self.current_theme))
        btn_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        # 新建任务按钮
        new_btn = wx.Button(btn_panel, label="新建任务")
        new_btn.SetFont(UIStyles.get_font('medium'))
        new_btn.Bind(wx.EVT_BUTTON, self.on_new_task)
        btn_sizer.Add(new_btn, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        # 动画测试按钮
        anim_btn = wx.Button(btn_panel, label="测试动画")
        anim_btn.SetFont(UIStyles.get_font('medium'))
        anim_btn.Bind(wx.EVT_BUTTON, self.on_test_animation)
        btn_sizer.Add(anim_btn, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        # 主题切换按钮
        theme_btn = wx.Button(btn_panel, label="切换主题")
        theme_btn.SetFont(UIStyles.get_font('medium'))
        theme_btn.Bind(wx.EVT_BUTTON, self.on_toggle_theme)
        btn_sizer.Add(theme_btn, 0, wx.ALL, UIStyles.SPACING['sm'])
        
        btn_panel.SetSizer(btn_sizer)
        sizer.Add(btn_panel, 0, wx.CENTER | wx.ALL, UIStyles.SPACING['md'])
    
    def setup_events(self) -> None:
        """设置事件处理"""
        self.Bind(wx.EVT_CLOSE, self.on_close)
    
    def on_card_hover(self, event, card) -> None:
        """卡片悬停事件"""
        self.animation_manager.scale_animation(card, 1.02, 150)
    
    def on_card_leave(self, event, card) -> None:
        """卡片离开事件"""
        pass  # 动画会自动恢复
    
    def on_new_task(self, event) -> None:
        """新建任务事件"""
        dlg = wx.TextEntryDialog(self, "请输入任务标题:", "新建任务")
        if dlg.ShowModal() == wx.ID_OK:
            title = dlg.GetValue().strip()
            if title:
                wx.MessageBox(f"任务 '{title}' 创建成功！\n(这是测试版本)", "成功", wx.OK | wx.ICON_INFORMATION)
        dlg.Destroy()
    
    def on_test_animation(self, event) -> None:
        """测试动画事件"""
        # 对第一个卡片执行动画
        cards = [child for child in self.main_panel.GetChildren() if isinstance(child, wx.Panel)]
        if len(cards) > 1:  # 第二个panel是卡片容器
            card_container = cards[1]
            first_card = card_container.GetChildren()[0] if card_container.GetChildren() else None
            if first_card:
                self.animation_manager.completion_animation(first_card)
    
    def on_toggle_theme(self, event) -> None:
        """切换主题事件"""
        self.current_theme = 'dark' if self.current_theme == 'light' else 'light'
        wx.MessageBox(f"主题已切换到: {self.current_theme}\n(重启应用生效)", "主题切换", wx.OK | wx.ICON_INFORMATION)
    
    def on_close(self, event) -> None:
        """关闭事件"""
        self.Destroy()


class TestApp(wx.App):
    """测试应用程序类"""
    
    def OnInit(self):
        """应用程序初始化"""
        try:
            # 创建主窗口
            self.main_window = SimpleMainWindow()
            self.main_window.Show()
            
            # 设置顶级窗口
            self.SetTopWindow(self.main_window)
            
            return True
            
        except Exception as e:
            wx.MessageBox(f"应用程序启动失败: {str(e)}", "错误", wx.OK | wx.ICON_ERROR)
            return False


def main():
    """主函数"""
    # 创建应用程序实例
    app = TestApp()
    
    # 运行应用程序
    app.MainLoop()


if __name__ == "__main__":
    main()
