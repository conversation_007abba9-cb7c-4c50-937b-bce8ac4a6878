"""
动画系统
"""
import wx
import math
from typing import Callable, Optional, Any


class AnimationManager:
    """动画管理器"""
    
    def __init__(self):
        """初始化动画管理器"""
        self.active_animations = {}
    
    def fade_in(self, widget: wx.Window, duration: int = 300, 
                callback: Optional[Callable] = None) -> None:
        """淡入动画"""
        self._animate_opacity(widget, 0, 255, duration, callback)
    
    def fade_out(self, widget: wx.Window, duration: int = 300,
                 callback: Optional[Callable] = None) -> None:
        """淡出动画"""
        self._animate_opacity(widget, 255, 0, duration, callback)
    
    def _animate_opacity(self, widget: wx.Window, start_opacity: int, 
                        end_opacity: int, duration: int,
                        callback: Optional[Callable] = None) -> None:
        """透明度动画"""
        if not widget or widget.IsBeingDeleted():
            return
        
        animation_id = id(widget)
        start_time = wx.GetLocalTimeMillis()
        
        def update_opacity():
            if animation_id not in self.active_animations:
                return
            
            current_time = wx.GetLocalTimeMillis()
            elapsed = current_time - start_time
            progress = min(elapsed / duration, 1.0)
            
            # 使用缓动函数
            eased_progress = self._ease_in_out_cubic(progress)
            
            # 计算当前透明度
            current_opacity = start_opacity + (end_opacity - start_opacity) * eased_progress
            
            # 设置透明度
            if hasattr(widget, 'SetTransparent'):
                widget.SetTransparent(int(current_opacity))
            
            # 刷新显示
            widget.Refresh()
            
            if progress >= 1.0:
                # 动画完成
                del self.active_animations[animation_id]
                if callback:
                    callback()
            else:
                # 继续动画
                wx.CallLater(16, update_opacity)  # 约60FPS
        
        self.active_animations[animation_id] = True
        update_opacity()
    
    def slide_in(self, widget: wx.Window, direction: str = 'left', 
                 duration: int = 400, callback: Optional[Callable] = None) -> None:
        """滑入动画"""
        if not widget or widget.IsBeingDeleted():
            return
        
        # 获取目标位置
        target_pos = widget.GetPosition()
        
        # 计算起始位置
        if direction == 'left':
            start_pos = wx.Point(target_pos.x - widget.GetSize().width, target_pos.y)
        elif direction == 'right':
            start_pos = wx.Point(target_pos.x + widget.GetSize().width, target_pos.y)
        elif direction == 'top':
            start_pos = wx.Point(target_pos.x, target_pos.y - widget.GetSize().height)
        else:  # bottom
            start_pos = wx.Point(target_pos.x, target_pos.y + widget.GetSize().height)
        
        # 设置起始位置
        widget.SetPosition(start_pos)
        
        self._animate_position(widget, start_pos, target_pos, duration, callback)
    
    def slide_out(self, widget: wx.Window, direction: str = 'right',
                  duration: int = 400, callback: Optional[Callable] = None) -> None:
        """滑出动画"""
        if not widget or widget.IsBeingDeleted():
            return
        
        # 获取起始位置
        start_pos = widget.GetPosition()
        
        # 计算目标位置
        if direction == 'left':
            target_pos = wx.Point(start_pos.x - widget.GetSize().width, start_pos.y)
        elif direction == 'right':
            target_pos = wx.Point(start_pos.x + widget.GetSize().width, start_pos.y)
        elif direction == 'top':
            target_pos = wx.Point(start_pos.x, start_pos.y - widget.GetSize().height)
        else:  # bottom
            target_pos = wx.Point(start_pos.x, start_pos.y + widget.GetSize().height)
        
        self._animate_position(widget, start_pos, target_pos, duration, callback)
    
    def _animate_position(self, widget: wx.Window, start_pos: wx.Point,
                         target_pos: wx.Point, duration: int,
                         callback: Optional[Callable] = None) -> None:
        """位置动画"""
        animation_id = id(widget)
        start_time = wx.GetLocalTimeMillis()
        
        def update_position():
            if animation_id not in self.active_animations:
                return
            
            current_time = wx.GetLocalTimeMillis()
            elapsed = current_time - start_time
            progress = min(elapsed / duration, 1.0)
            
            # 使用缓动函数
            eased_progress = self._ease_in_out_cubic(progress)
            
            # 计算当前位置
            current_x = start_pos.x + (target_pos.x - start_pos.x) * eased_progress
            current_y = start_pos.y + (target_pos.y - start_pos.y) * eased_progress
            
            # 设置位置
            widget.SetPosition(wx.Point(int(current_x), int(current_y)))
            
            if progress >= 1.0:
                # 动画完成
                del self.active_animations[animation_id]
                if callback:
                    callback()
            else:
                # 继续动画
                wx.CallLater(16, update_position)
        
        self.active_animations[animation_id] = True
        update_position()
    
    def scale_animation(self, widget: wx.Window, scale_factor: float = 1.05,
                       duration: int = 200, callback: Optional[Callable] = None) -> None:
        """缩放动画"""
        if not widget or widget.IsBeingDeleted():
            return
        
        original_size = widget.GetSize()
        target_size = wx.Size(
            int(original_size.width * scale_factor),
            int(original_size.height * scale_factor)
        )
        
        # 先放大再缩小
        def scale_back():
            self._animate_size(widget, target_size, original_size, duration // 2, callback)
        
        self._animate_size(widget, original_size, target_size, duration // 2, scale_back)
    
    def _animate_size(self, widget: wx.Window, start_size: wx.Size,
                     target_size: wx.Size, duration: int,
                     callback: Optional[Callable] = None) -> None:
        """尺寸动画"""
        animation_id = id(widget)
        start_time = wx.GetLocalTimeMillis()
        
        def update_size():
            if animation_id not in self.active_animations:
                return
            
            current_time = wx.GetLocalTimeMillis()
            elapsed = current_time - start_time
            progress = min(elapsed / duration, 1.0)
            
            # 使用缓动函数
            eased_progress = self._ease_in_out_cubic(progress)
            
            # 计算当前尺寸
            current_width = start_size.width + (target_size.width - start_size.width) * eased_progress
            current_height = start_size.height + (target_size.height - start_size.height) * eased_progress
            
            # 设置尺寸
            widget.SetSize(wx.Size(int(current_width), int(current_height)))
            
            if progress >= 1.0:
                # 动画完成
                del self.active_animations[animation_id]
                if callback:
                    callback()
            else:
                # 继续动画
                wx.CallLater(16, update_size)
        
        self.active_animations[animation_id] = True
        update_size()
    
    def completion_animation(self, widget: wx.Window, callback: Optional[Callable] = None) -> None:
        """任务完成动画"""
        # 先缩放然后淡出
        def fade_out_callback():
            self.fade_out(widget, 300, callback)
        
        self.scale_animation(widget, 0.95, 200, fade_out_callback)
    
    def stop_animation(self, widget: wx.Window) -> None:
        """停止指定控件的动画"""
        animation_id = id(widget)
        if animation_id in self.active_animations:
            del self.active_animations[animation_id]
    
    def stop_all_animations(self) -> None:
        """停止所有动画"""
        self.active_animations.clear()
    
    @staticmethod
    def _ease_in_out_cubic(t: float) -> float:
        """三次缓动函数"""
        if t < 0.5:
            return 4 * t * t * t
        else:
            return 1 - pow(-2 * t + 2, 3) / 2
