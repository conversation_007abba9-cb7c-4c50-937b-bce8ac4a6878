"""
通知管理器
"""
import schedule
import time
import threading
from datetime import datetime, timedelta
from typing import List, Callable, Optional
from plyer import notification
from core.models import Todo


class NotificationManager:
    """通知管理类"""
    
    def __init__(self):
        """初始化通知管理器"""
        self.is_running = False
        self.scheduler_thread = None
        self.notification_callbacks = []
        self.sound_enabled = True
        
    def start_scheduler(self) -> None:
        """启动定时任务调度器"""
        if not self.is_running:
            self.is_running = True
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()
    
    def stop_scheduler(self) -> None:
        """停止定时任务调度器"""
        self.is_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=1)
    
    def _run_scheduler(self) -> None:
        """运行调度器"""
        while self.is_running:
            schedule.run_pending()
            time.sleep(1)
    
    def setup_todo_reminders(self, get_todos_callback: Callable[[], List[Todo]]) -> None:
        """设置任务提醒"""
        # 每分钟检查一次到期任务
        schedule.every().minute.do(self._check_due_todos, get_todos_callback)
        
        # 每天早上9点提醒今日任务
        schedule.every().day.at("09:00").do(self._daily_reminder, get_todos_callback)
    
    def _check_due_todos(self, get_todos_callback: Callable[[], List[Todo]]) -> None:
        """检查到期任务"""
        try:
            todos = get_todos_callback()
            now = datetime.now()
            
            for todo in todos:
                if todo.due_date and not todo.completed:
                    # 检查是否在提醒时间范围内（提前15分钟提醒）
                    time_diff = todo.due_date - now
                    if timedelta(minutes=0) <= time_diff <= timedelta(minutes=15):
                        self.show_notification(
                            title="任务提醒",
                            message=f"任务 '{todo.title}' 将在 {time_diff.seconds // 60} 分钟后到期",
                            timeout=10
                        )
                    elif time_diff < timedelta(minutes=0):
                        # 已过期
                        self.show_notification(
                            title="任务过期",
                            message=f"任务 '{todo.title}' 已过期",
                            timeout=10
                        )
        except Exception as e:
            print(f"检查到期任务时出错: {e}")
    
    def _daily_reminder(self, get_todos_callback: Callable[[], List[Todo]]) -> None:
        """每日提醒"""
        try:
            todos = get_todos_callback()
            today_todos = [todo for todo in todos if not todo.completed and 
                          todo.due_date and todo.due_date.date() == datetime.now().date()]
            
            if today_todos:
                self.show_notification(
                    title="今日任务提醒",
                    message=f"您今天有 {len(today_todos)} 个任务需要完成",
                    timeout=15
                )
        except Exception as e:
            print(f"每日提醒时出错: {e}")
    
    def show_notification(self, title: str, message: str, timeout: int = 5) -> None:
        """显示系统通知"""
        try:
            notification.notify(
                title=title,
                message=message,
                timeout=timeout,
                app_name="Todo List",
                app_icon=None  # 可以设置应用图标路径
            )
            
            # 触发回调函数
            for callback in self.notification_callbacks:
                callback(title, message)
                
        except Exception as e:
            print(f"显示通知时出错: {e}")
    
    def add_notification_callback(self, callback: Callable[[str, str], None]) -> None:
        """添加通知回调函数"""
        self.notification_callbacks.append(callback)
    
    def remove_notification_callback(self, callback: Callable[[str, str], None]) -> None:
        """移除通知回调函数"""
        if callback in self.notification_callbacks:
            self.notification_callbacks.remove(callback)
    
    def schedule_custom_reminder(self, todo: Todo, reminder_time: datetime) -> None:
        """安排自定义提醒"""
        def reminder_job():
            if not todo.completed:
                self.show_notification(
                    title="自定义提醒",
                    message=f"提醒: {todo.title}",
                    timeout=10
                )
        
        # 计算延迟时间
        delay = (reminder_time - datetime.now()).total_seconds()
        if delay > 0:
            timer = threading.Timer(delay, reminder_job)
            timer.daemon = True
            timer.start()
    
    def set_sound_enabled(self, enabled: bool) -> None:
        """设置是否启用提醒音效"""
        self.sound_enabled = enabled
