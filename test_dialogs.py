"""
对话框测试程序 - 独立测试版本
"""
import wx
import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟数据模型
class MockTodo:
    def __init__(self, id=1, title="测试任务", description="这是一个测试任务", 
                 priority=2, category_id=1, folder_id=1, due_date=None, completed=False):
        self.id = id
        self.title = title
        self.description = description
        self.priority = priority
        self.category_id = category_id
        self.folder_id = folder_id
        self.due_date = due_date
        self.completed = completed
    
    @property
    def priority_text(self):
        return {1: "低", 2: "中", 3: "高"}.get(self.priority, "低")
    
    @property
    def priority_color(self):
        return {1: "#95a5a6", 2: "#f39c12", 3: "#e74c3c"}.get(self.priority, "#95a5a6")
    
    @property
    def is_overdue(self):
        if self.due_date and not self.completed:
            return datetime.now() > self.due_date
        return False

class MockCategory:
    def __init__(self, id, name, color="#3498db", icon="💼"):
        self.id = id
        self.name = name
        self.color = color
        self.icon = icon

class MockFolder:
    def __init__(self, id, name, color="#95a5a6"):
        self.id = id
        self.name = name
        self.color = color


class DialogTestWindow(wx.Frame):
    """对话框测试窗口"""
    
    def __init__(self):
        super().__init__(None, title="Todo List 对话框测试", size=(800, 600))
        
        self.current_theme = 'light'
        
        # 模拟数据
        self.mock_categories = [
            MockCategory(1, "工作", "#e74c3c", "💼"),
            MockCategory(2, "个人", "#2ecc71", "👤"),
            MockCategory(3, "学习", "#f39c12", "📚"),
            MockCategory(4, "购物", "#9b59b6", "🛒")
        ]
        
        self.mock_folders = [
            MockFolder(1, "重要", "#e74c3c"),
            MockFolder(2, "今天", "#f39c12"),
            MockFolder(3, "本周", "#3498db")
        ]
        
        self.mock_todo = MockTodo(
            id=1,
            title="示例任务",
            description="这是一个示例任务，用于测试编辑对话框",
            priority=2,
            category_id=1,
            folder_id=1,
            due_date=datetime.now() + timedelta(days=1)
        )
        
        self.init_ui()
        self.Center()
    
    def init_ui(self):
        """初始化用户界面"""
        panel = wx.Panel(self)
        panel.SetBackgroundColour(wx.Colour("#f8f9fa"))
        
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题
        title = wx.StaticText(panel, label="Todo List 对话框功能测试")
        title.SetFont(wx.Font(16, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        sizer.Add(title, 0, wx.ALL | wx.CENTER, 20)
        
        # 说明文本
        desc = wx.StaticText(panel, label="点击下面的按钮测试各种对话框功能")
        desc.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        sizer.Add(desc, 0, wx.ALL | wx.CENTER, 10)
        
        # 按钮区域
        button_sizer = wx.GridSizer(2, 2, 10, 10)
        
        # 添加任务对话框按钮
        add_btn = wx.Button(panel, label="测试添加任务对话框", size=(200, 50))
        add_btn.Bind(wx.EVT_BUTTON, self.on_test_add_dialog)
        button_sizer.Add(add_btn, 0, wx.EXPAND)
        
        # 编辑任务对话框按钮
        edit_btn = wx.Button(panel, label="测试编辑任务对话框", size=(200, 50))
        edit_btn.Bind(wx.EVT_BUTTON, self.on_test_edit_dialog)
        button_sizer.Add(edit_btn, 0, wx.EXPAND)
        
        # 设置对话框按钮
        settings_btn = wx.Button(panel, label="测试设置对话框", size=(200, 50))
        settings_btn.Bind(wx.EVT_BUTTON, self.on_test_settings_dialog)
        button_sizer.Add(settings_btn, 0, wx.EXPAND)
        
        # 分类管理对话框按钮
        category_btn = wx.Button(panel, label="测试分类管理对话框", size=(200, 50))
        category_btn.Bind(wx.EVT_BUTTON, self.on_test_category_dialog)
        button_sizer.Add(category_btn, 0, wx.EXPAND)
        
        sizer.Add(button_sizer, 0, wx.ALL | wx.CENTER, 20)
        
        # 结果显示区域
        self.result_text = wx.TextCtrl(panel, style=wx.TE_MULTILINE | wx.TE_READONLY, size=(-1, 200))
        self.result_text.SetFont(wx.Font(9, wx.FONTFAMILY_TELETYPE, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        sizer.Add(self.result_text, 1, wx.EXPAND | wx.ALL, 20)
        
        panel.SetSizer(sizer)
    
    def on_test_add_dialog(self, event):
        """测试添加任务对话框"""
        try:
            from ui.components.add_todo_dialog import AddTodoDialog
            
            dlg = AddTodoDialog(self, self.mock_categories, self.mock_folders, self.current_theme)
            result = dlg.ShowModal()
            
            if result == wx.ID_OK:
                todo_data = dlg.get_todo_data()
                self.log_result("添加任务对话框", "确定", todo_data)
            else:
                self.log_result("添加任务对话框", "取消", None)
            
            dlg.Destroy()
            
        except Exception as e:
            self.log_error("添加任务对话框测试失败", str(e))
    
    def on_test_edit_dialog(self, event):
        """测试编辑任务对话框"""
        try:
            from ui.components.edit_todo_dialog import EditTodoDialog
            
            dlg = EditTodoDialog(self, self.mock_todo, self.mock_categories, self.mock_folders, self.current_theme)
            result = dlg.ShowModal()
            
            if result == wx.ID_OK:
                todo_data = dlg.get_todo_data()
                self.log_result("编辑任务对话框", "保存", todo_data)
            elif result == wx.ID_DELETE:
                self.log_result("编辑任务对话框", "删除", {"todo_id": dlg.get_todo_id()})
            else:
                self.log_result("编辑任务对话框", "取消", None)
            
            dlg.Destroy()
            
        except Exception as e:
            self.log_error("编辑任务对话框测试失败", str(e))
    
    def on_test_settings_dialog(self, event):
        """测试设置对话框"""
        try:
            from ui.components.settings_dialog import SettingsDialog
            
            current_settings = {
                'theme': 'light',
                'auto_start': False,
                'notifications_enabled': True,
                'sound_enabled': True,
                'font_size': 'normal'
            }
            
            dlg = SettingsDialog(self, current_settings, self.current_theme)
            result = dlg.ShowModal()
            
            if result == wx.ID_OK:
                settings_data = dlg.get_settings_data()
                self.log_result("设置对话框", "确定", settings_data)
            else:
                self.log_result("设置对话框", "取消", None)
            
            dlg.Destroy()
            
        except Exception as e:
            self.log_error("设置对话框测试失败", str(e))
    
    def on_test_category_dialog(self, event):
        """测试分类管理对话框"""
        try:
            from ui.components.category_manager_dialog import CategoryManagerDialog
            
            dlg = CategoryManagerDialog(self, self.mock_categories, self.current_theme)
            result = dlg.ShowModal()
            
            if result == wx.ID_OK:
                modified = dlg.get_modified_categories()
                deleted = dlg.get_deleted_categories()
                all_categories = dlg.get_all_categories()
                
                result_data = {
                    'modified_count': len(modified),
                    'deleted_count': len(deleted),
                    'total_count': len(all_categories)
                }
                self.log_result("分类管理对话框", "确定", result_data)
            else:
                self.log_result("分类管理对话框", "取消", None)
            
            dlg.Destroy()
            
        except Exception as e:
            self.log_error("分类管理对话框测试失败", str(e))
    
    def log_result(self, dialog_name, action, data):
        """记录测试结果"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_text = f"[{timestamp}] {dialog_name} - {action}\n"
        
        if data:
            log_text += f"返回数据: {data}\n"
        
        log_text += "-" * 50 + "\n"
        
        self.result_text.AppendText(log_text)
    
    def log_error(self, test_name, error_msg):
        """记录错误"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_text = f"[{timestamp}] 错误: {test_name}\n"
        log_text += f"错误信息: {error_msg}\n"
        log_text += "-" * 50 + "\n"
        
        self.result_text.AppendText(log_text)


class DialogTestApp(wx.App):
    """对话框测试应用"""
    
    def OnInit(self):
        try:
            self.main_window = DialogTestWindow()
            self.main_window.Show()
            self.SetTopWindow(self.main_window)
            return True
        except Exception as e:
            wx.MessageBox(f"应用启动失败: {str(e)}", "错误", wx.OK | wx.ICON_ERROR)
            return False


def main():
    """主函数"""
    app = DialogTestApp()
    app.MainLoop()


if __name__ == "__main__":
    main()
