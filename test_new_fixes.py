"""
新修复功能测试程序
"""
import wx
import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟数据模型和管理器
class MockCategory:
    def __init__(self, id, name, color="#3498db", icon="💼"):
        self.id = id
        self.name = name
        self.color = color
        self.icon = icon

class MockFolder:
    def __init__(self, id, name, color="#95a5a6", parent_id=None):
        self.id = id
        self.name = name
        self.color = color
        self.parent_id = parent_id

class MockCategoryManager:
    def __init__(self):
        self.categories = []
        self.next_id = 1
    
    def get_categories(self):
        return self.categories
    
    def add_category(self, name, color="#3498db", icon="💼"):
        new_category = MockCategory(self.next_id, name, color, icon)
        self.categories.append(new_category)
        self.next_id += 1
        return new_category.id
    
    def update_category(self, category_id, name=None, color=None, icon=None):
        for category in self.categories:
            if category.id == category_id:
                if name: category.name = name
                if color: category.color = color
                if icon: category.icon = icon
                return True
        return False
    
    def delete_category(self, category_id):
        self.categories = [c for c in self.categories if c.id != category_id]
        return True
    
    def create_default_categories(self):
        if len(self.categories) == 0:
            defaults = [
                ("工作", "#e74c3c", "💼"),
                ("个人", "#2ecc71", "👤"),
                ("学习", "#f39c12", "📚"),
                ("购物", "#9b59b6", "🛒")
            ]
            for name, color, icon in defaults:
                self.add_category(name, color, icon)

class MockFolderManager:
    def __init__(self):
        self.folders = []
        self.next_id = 1
    
    def get_folders(self):
        return self.folders
    
    def get_root_folders(self):
        return [f for f in self.folders if f.parent_id is None]
    
    def get_folders_by_parent(self, parent_id=None):
        return [f for f in self.folders if f.parent_id == parent_id]
    
    def folder_exists(self, name, parent_id=None):
        folders = self.get_folders_by_parent(parent_id)
        return any(f.name.lower() == name.lower() for f in folders)
    
    def add_folder(self, name, parent_id=None, color="#95a5a6"):
        if self.folder_exists(name, parent_id):
            raise ValueError(f"文件夹 '{name}' 已存在")
        
        new_folder = MockFolder(self.next_id, name, color, parent_id)
        self.folders.append(new_folder)
        self.next_id += 1
        return new_folder.id
    
    def delete_folder(self, folder_id):
        self.folders = [f for f in self.folders if f.id != folder_id]
        return True
    
    def get_folder_tree(self):
        root_folders = self.get_root_folders()
        return [{'folder': f, 'children': [], 'task_count': 0} for f in root_folders]
    
    def create_default_folders(self):
        created_count = 0
        defaults = [
            ("重要", "#e74c3c"),
            ("今天", "#f39c12"),
            ("本周", "#3498db"),
            ("下周", "#9b59b6"),
            ("稍后", "#95a5a6")
        ]
        
        existing_names = {f.name.lower() for f in self.get_root_folders()}
        
        for name, color in defaults:
            if name.lower() not in existing_names:
                try:
                    self.add_folder(name, None, color)
                    created_count += 1
                except ValueError:
                    continue
        
        return created_count


class NewFixTestWindow(wx.Frame):
    """新修复功能测试窗口"""
    
    def __init__(self):
        super().__init__(None, title="Todo List 新修复功能测试", size=(800, 600))
        
        # 模拟管理器
        self.category_manager = MockCategoryManager()
        self.folder_manager = MockFolderManager()
        
        self.current_theme = 'light'
        
        self.init_ui()
        self.Center()
    
    def init_ui(self):
        """初始化用户界面"""
        panel = wx.Panel(self)
        panel.SetBackgroundColour(wx.Colour("#f8f9fa"))
        
        sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题
        title = wx.StaticText(panel, label="Todo List 新修复功能测试")
        title.SetFont(wx.Font(16, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        sizer.Add(title, 0, wx.ALL | wx.CENTER, 20)
        
        # 测试按钮网格
        button_sizer = wx.GridSizer(3, 3, 10, 10)
        
        # 重复文件夹创建测试
        folder_test1_btn = wx.Button(panel, label="测试默认文件夹创建", size=(180, 50))
        folder_test1_btn.Bind(wx.EVT_BUTTON, self.test_default_folders)
        button_sizer.Add(folder_test1_btn, 0, wx.EXPAND)
        
        folder_test2_btn = wx.Button(panel, label="测试重复文件夹防护", size=(180, 50))
        folder_test2_btn.Bind(wx.EVT_BUTTON, self.test_duplicate_folders)
        button_sizer.Add(folder_test2_btn, 0, wx.EXPAND)
        
        # 类别保存测试
        category_test1_btn = wx.Button(panel, label="测试类别创建保存", size=(180, 50))
        category_test1_btn.Bind(wx.EVT_BUTTON, self.test_category_save)
        button_sizer.Add(category_test1_btn, 0, wx.EXPAND)
        
        category_test2_btn = wx.Button(panel, label="测试类别管理对话框", size=(180, 50))
        category_test2_btn.Bind(wx.EVT_BUTTON, self.test_category_dialog)
        button_sizer.Add(category_test2_btn, 0, wx.EXPAND)
        
        # 对话框尺寸测试
        dialog_test1_btn = wx.Button(panel, label="测试对话框尺寸", size=(180, 50))
        dialog_test1_btn.Bind(wx.EVT_BUTTON, self.test_dialog_size)
        button_sizer.Add(dialog_test1_btn, 0, wx.EXPAND)
        
        # 综合测试
        comprehensive_btn = wx.Button(panel, label="综合测试", size=(180, 50))
        comprehensive_btn.Bind(wx.EVT_BUTTON, self.test_comprehensive)
        button_sizer.Add(comprehensive_btn, 0, wx.EXPAND)
        
        # 清空数据测试
        clear_btn = wx.Button(panel, label="清空测试数据", size=(180, 50))
        clear_btn.Bind(wx.EVT_BUTTON, self.clear_test_data)
        button_sizer.Add(clear_btn, 0, wx.EXPAND)
        
        # 显示数据状态
        status_btn = wx.Button(panel, label="显示数据状态", size=(180, 50))
        status_btn.Bind(wx.EVT_BUTTON, self.show_data_status)
        button_sizer.Add(status_btn, 0, wx.EXPAND)
        
        sizer.Add(button_sizer, 0, wx.ALL | wx.CENTER, 20)
        
        # 结果显示区域
        self.result_text = wx.TextCtrl(panel, style=wx.TE_MULTILINE | wx.TE_READONLY, size=(-1, 250))
        self.result_text.SetFont(wx.Font(9, wx.FONTFAMILY_TELETYPE, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        sizer.Add(self.result_text, 1, wx.EXPAND | wx.ALL, 20)
        
        panel.SetSizer(sizer)
    
    def log_result(self, test_name, result, details=""):
        """记录测试结果"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        status = "✅ 成功" if result else "❌ 失败"
        log_text = f"[{timestamp}] {test_name}: {status}\n"
        if details:
            log_text += f"详情: {details}\n"
        log_text += "-" * 50 + "\n"
        
        self.result_text.AppendText(log_text)
    
    def test_default_folders(self, event):
        """测试默认文件夹创建"""
        try:
            initial_count = len(self.folder_manager.get_root_folders())
            created_count = self.folder_manager.create_default_folders()
            final_count = len(self.folder_manager.get_root_folders())
            
            self.log_result("默认文件夹创建", True, 
                          f"初始: {initial_count}, 创建: {created_count}, 最终: {final_count}")
        except Exception as e:
            self.log_result("默认文件夹创建", False, str(e))
    
    def test_duplicate_folders(self, event):
        """测试重复文件夹防护"""
        try:
            # 尝试创建重复文件夹
            try:
                self.folder_manager.add_folder("重要")
                self.log_result("重复文件夹防护", False, "应该阻止重复创建但没有")
            except ValueError as e:
                self.log_result("重复文件夹防护", True, f"正确阻止重复创建: {e}")
        except Exception as e:
            self.log_result("重复文件夹防护", False, str(e))
    
    def test_category_save(self, event):
        """测试类别创建保存"""
        try:
            initial_count = len(self.category_manager.get_categories())
            category_id = self.category_manager.add_category("测试分类", "#ff6b6b", "🧪")
            final_count = len(self.category_manager.get_categories())
            
            self.log_result("类别创建保存", category_id > 0, 
                          f"初始: {initial_count}, 新ID: {category_id}, 最终: {final_count}")
        except Exception as e:
            self.log_result("类别创建保存", False, str(e))
    
    def test_category_dialog(self, event):
        """测试类别管理对话框"""
        try:
            from ui.components.category_manager_dialog import CategoryManagerDialog
            categories = self.category_manager.get_categories()
            dlg = CategoryManagerDialog(self, categories, self.current_theme)
            
            # 检查对话框尺寸
            size = dlg.GetSize()
            min_size = dlg.GetMinSize()
            
            result = dlg.ShowModal()
            
            # 处理结果
            if result == wx.ID_OK:
                new_categories = dlg.get_new_categories()
                modified_categories = dlg.get_modified_categories()
                deleted_categories = dlg.get_deleted_categories()
                
                details = f"尺寸: {size}, 最小: {min_size}, 新增: {len(new_categories)}, 修改: {len(modified_categories)}, 删除: {len(deleted_categories)}"
                self.log_result("类别管理对话框", True, details)
            else:
                self.log_result("类别管理对话框", True, f"用户取消, 尺寸: {size}, 最小: {min_size}")
            
            dlg.Destroy()
        except Exception as e:
            self.log_result("类别管理对话框", False, str(e))
    
    def test_dialog_size(self, event):
        """测试对话框尺寸"""
        try:
            from ui.components.category_manager_dialog import CategoryManagerDialog
            dlg = CategoryManagerDialog(self, [], self.current_theme)
            
            size = dlg.GetSize()
            min_size = dlg.GetMinSize()
            
            # 检查尺寸是否合适
            width_ok = size[0] >= 650
            height_ok = size[1] >= 550
            min_width_ok = min_size[0] >= 650
            min_height_ok = min_size[1] >= 550
            
            all_ok = width_ok and height_ok and min_width_ok and min_height_ok
            
            details = f"尺寸: {size} (宽度{'✓' if width_ok else '✗'}, 高度{'✓' if height_ok else '✗'}), 最小: {min_size} (宽度{'✓' if min_width_ok else '✗'}, 高度{'✓' if min_height_ok else '✗'})"
            
            self.log_result("对话框尺寸", all_ok, details)
            dlg.Destroy()
        except Exception as e:
            self.log_result("对话框尺寸", False, str(e))
    
    def test_comprehensive(self, event):
        """综合测试"""
        self.log_result("综合测试", True, "开始综合测试...")
        
        # 测试1: 默认数据创建
        self.test_default_folders(None)
        wx.CallLater(100, self._comprehensive_test_2)
    
    def _comprehensive_test_2(self):
        """综合测试第2步"""
        # 测试2: 重复防护
        self.test_duplicate_folders(None)
        wx.CallLater(100, self._comprehensive_test_3)
    
    def _comprehensive_test_3(self):
        """综合测试第3步"""
        # 测试3: 类别保存
        self.test_category_save(None)
        wx.CallLater(100, self._comprehensive_test_4)
    
    def _comprehensive_test_4(self):
        """综合测试第4步"""
        # 测试4: 对话框尺寸
        self.test_dialog_size(None)
        self.log_result("综合测试", True, "综合测试完成！")
    
    def clear_test_data(self, event):
        """清空测试数据"""
        self.category_manager = MockCategoryManager()
        self.folder_manager = MockFolderManager()
        self.log_result("清空测试数据", True, "所有测试数据已清空")
    
    def show_data_status(self, event):
        """显示数据状态"""
        categories = self.category_manager.get_categories()
        folders = self.folder_manager.get_folders()
        
        cat_names = [c.name for c in categories]
        folder_names = [f.name for f in folders]
        
        details = f"分类({len(categories)}): {cat_names}, 文件夹({len(folders)}): {folder_names}"
        self.log_result("数据状态", True, details)


class NewFixTestApp(wx.App):
    """新修复测试应用"""
    
    def OnInit(self):
        try:
            self.main_window = NewFixTestWindow()
            self.main_window.Show()
            self.SetTopWindow(self.main_window)
            return True
        except Exception as e:
            wx.MessageBox(f"应用启动失败: {str(e)}", "错误", wx.OK | wx.ICON_ERROR)
            return False


def main():
    """主函数"""
    app = NewFixTestApp()
    app.MainLoop()


if __name__ == "__main__":
    main()
