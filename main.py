"""
Todo-List应用程序主入口
"""
import wx
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.main_window import MainWindow


class TodoApp(wx.App):
    """Todo应用程序类"""
    
    def OnInit(self):
        """应用程序初始化"""
        try:
            # 创建主窗口
            self.main_window = MainWindow()
            self.main_window.Show()
            
            # 设置顶级窗口
            self.SetTopWindow(self.main_window)
            
            return True
            
        except Exception as e:
            wx.MessageBox(f"应用程序启动失败: {str(e)}", "错误", wx.OK | wx.ICON_ERROR)
            return False
    
    def OnExit(self):
        """应用程序退出"""
        return 0


def main():
    """主函数"""
    # 创建应用程序实例
    app = TodoApp()
    
    # 运行应用程序
    app.MainLoop()


if __name__ == "__main__":
    main()
