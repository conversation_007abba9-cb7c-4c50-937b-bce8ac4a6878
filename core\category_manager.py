"""
分类管理器
"""
from typing import List, Optional
from .database import DatabaseManager
from .models import Category


class CategoryManager:
    """分类管理类"""
    
    def __init__(self, db_manager: DatabaseManager):
        """初始化分类管理器"""
        self.db = db_manager
    
    def add_category(self, name: str, color: str = '#3498db', icon: str = '💼') -> int:
        """添加分类"""
        # 验证分类名称
        if not name or not name.strip():
            raise ValueError("分类名称不能为空")
        
        # 检查是否已存在同名分类
        existing_categories = self.get_categories()
        for category in existing_categories:
            if category.name.lower() == name.strip().lower():
                raise ValueError(f"分类 '{name}' 已存在")
        
        return self.db.add_category(name.strip(), color, icon)
    
    def update_category(self, category_id: int, name: str = None, 
                       color: str = None, icon: str = None) -> bool:
        """更新分类"""
        # 验证分类是否存在
        category = self.get_category_by_id(category_id)
        if not category:
            raise ValueError(f"分类ID {category_id} 不存在")
        
        # 如果更新名称，检查是否与其他分类重名
        if name and name.strip():
            existing_categories = self.get_categories()
            for cat in existing_categories:
                if cat.id != category_id and cat.name.lower() == name.strip().lower():
                    raise ValueError(f"分类 '{name}' 已存在")
        
        return self.db.update_category(category_id, name, color, icon)
    
    def delete_category(self, category_id: int) -> bool:
        """删除分类"""
        # 验证分类是否存在
        category = self.get_category_by_id(category_id)
        if not category:
            raise ValueError(f"分类ID {category_id} 不存在")
        
        # 检查是否有任务使用此分类
        todos_count = self._get_todos_count_by_category(category_id)
        if todos_count > 0:
            # 可以选择阻止删除或者提示用户
            print(f"警告: 分类 '{category.name}' 下有 {todos_count} 个任务，删除后这些任务将变为无分类")
        
        return self.db.delete_category(category_id)
    
    def get_categories(self) -> List[Category]:
        """获取所有分类"""
        return self.db.get_categories()
    
    def get_category_by_id(self, category_id: int) -> Optional[Category]:
        """根据ID获取分类"""
        return self.db.get_category_by_id(category_id)
    
    def get_category_by_name(self, name: str) -> Optional[Category]:
        """根据名称获取分类"""
        categories = self.get_categories()
        for category in categories:
            if category.name.lower() == name.lower():
                return category
        return None
    
    def get_categories_with_task_count(self) -> List[dict]:
        """获取分类及其任务数量"""
        categories = self.get_categories()
        result = []
        
        for category in categories:
            task_count = self._get_todos_count_by_category(category.id)
            result.append({
                'category': category,
                'task_count': task_count
            })
        
        return result
    
    def _get_todos_count_by_category(self, category_id: int) -> int:
        """获取指定分类下的任务数量"""
        query = "SELECT COUNT(*) as count FROM todos WHERE category_id = ?"
        rows = self.db.execute_query(query, (category_id,))
        return rows[0]['count'] if rows else 0
    
    def create_default_categories(self) -> None:
        """创建默认分类"""
        default_categories = [
            {'name': '工作', 'color': '#e74c3c', 'icon': '💼'},
            {'name': '个人', 'color': '#2ecc71', 'icon': '👤'},
            {'name': '学习', 'color': '#f39c12', 'icon': '📚'},
            {'name': '购物', 'color': '#9b59b6', 'icon': '🛒'},
            {'name': '健康', 'color': '#1abc9c', 'icon': '🏥'},
            {'name': '娱乐', 'color': '#e67e22', 'icon': '🎮'}
        ]
        
        existing_categories = self.get_categories()
        existing_names = {cat.name.lower() for cat in existing_categories}
        
        for cat_data in default_categories:
            if cat_data['name'].lower() not in existing_names:
                try:
                    self.add_category(cat_data['name'], cat_data['color'], cat_data['icon'])
                except ValueError:
                    # 如果添加失败，继续下一个
                    continue
    
    def export_categories(self) -> List[dict]:
        """导出分类数据"""
        categories = self.get_categories()
        return [
            {
                'name': cat.name,
                'color': cat.color,
                'icon': cat.icon
            }
            for cat in categories
        ]
    
    def import_categories(self, categories_data: List[dict]) -> int:
        """导入分类数据"""
        imported_count = 0
        
        for cat_data in categories_data:
            try:
                name = cat_data.get('name', '')
                color = cat_data.get('color', '#3498db')
                icon = cat_data.get('icon', '💼')
                
                if name:
                    self.add_category(name, color, icon)
                    imported_count += 1
            except ValueError:
                # 如果分类已存在或其他错误，跳过
                continue
        
        return imported_count
    
    def validate_category_data(self, name: str, color: str = None, icon: str = None) -> dict:
        """验证分类数据"""
        errors = []
        
        # 验证名称
        if not name or not name.strip():
            errors.append("分类名称不能为空")
        elif len(name.strip()) > 50:
            errors.append("分类名称不能超过50个字符")
        
        # 验证颜色
        if color and not color.startswith('#'):
            errors.append("颜色格式不正确，应为十六进制格式（如#3498db）")
        
        # 验证图标
        if icon and len(icon) > 10:
            errors.append("图标长度不能超过10个字符")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
