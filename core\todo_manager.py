"""
任务管理器
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from core.database import DatabaseManager
from core.models import Todo, Category, Folder


class TodoManager:
    """任务管理核心类"""
    
    def __init__(self, db_manager: DatabaseManager):
        """初始化任务管理器"""
        self.db = db_manager
    
    def add_todo(self, title: str, description: str = "", priority: int = 1, 
                 category_id: Optional[int] = None, folder_id: Optional[int] = None,
                 due_date: Optional[datetime] = None) -> int:
        """添加新任务"""
        query = """
        INSERT INTO todos (title, description, priority, category_id, folder_id, due_date)
        VALUES (?, ?, ?, ?, ?, ?)
        """
        params = (title, description, priority, category_id, folder_id, due_date)
        return self.db.execute_update(query, params)
    
    def update_todo(self, todo_id: int, **kwargs) -> bool:
        """更新任务"""
        # 构建动态更新语句
        fields = []
        values = []
        
        for key, value in kwargs.items():
            if key in ['title', 'description', 'priority', 'category_id', 'folder_id', 'due_date', 'completed']:
                fields.append(f"{key} = ?")
                values.append(value)
        
        if not fields:
            return False
        
        fields.append("updated_at = ?")
        values.append(datetime.now())
        values.append(todo_id)
        
        query = f"UPDATE todos SET {', '.join(fields)} WHERE id = ?"
        result = self.db.execute_update(query, tuple(values))
        return result != -1
    
    def delete_todo(self, todo_id: int) -> bool:
        """删除任务"""
        query = "DELETE FROM todos WHERE id = ?"
        result = self.db.execute_update(query, (todo_id,))
        return result != -1
    
    def get_todos(self, filter_params: Optional[Dict[str, Any]] = None) -> List[Todo]:
        """获取任务列表"""
        query = """
        SELECT t.*, c.name as category_name, c.color as category_color,
               f.name as folder_name, f.color as folder_color
        FROM todos t
        LEFT JOIN categories c ON t.category_id = c.id
        LEFT JOIN folders f ON t.folder_id = f.id
        """
        
        conditions = []
        params = []
        
        if filter_params:
            if 'completed' in filter_params:
                conditions.append("t.completed = ?")
                params.append(filter_params['completed'])
            
            if 'category_id' in filter_params:
                conditions.append("t.category_id = ?")
                params.append(filter_params['category_id'])
            
            if 'folder_id' in filter_params:
                conditions.append("t.folder_id = ?")
                params.append(filter_params['folder_id'])
            
            if 'priority' in filter_params:
                conditions.append("t.priority = ?")
                params.append(filter_params['priority'])
        
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        
        query += " ORDER BY t.priority DESC, t.created_at DESC"
        
        rows = self.db.execute_query(query, tuple(params))
        return [self._row_to_todo(row) for row in rows]
    
    def mark_completed(self, todo_id: int, completed: bool = True) -> bool:
        """标记任务完成状态"""
        return self.update_todo(todo_id, completed=completed)
    
    def get_overdue_todos(self) -> List[Todo]:
        """获取过期任务"""
        query = """
        SELECT * FROM todos 
        WHERE due_date < ? AND completed = 0
        ORDER BY due_date ASC
        """
        rows = self.db.execute_query(query, (datetime.now(),))
        return [self._row_to_todo(row) for row in rows]
    
    def get_todo_by_id(self, todo_id: int) -> Optional[Todo]:
        """根据ID获取任务"""
        query = "SELECT * FROM todos WHERE id = ?"
        rows = self.db.execute_query(query, (todo_id,))
        if rows:
            return self._row_to_todo(rows[0])
        return None
    
    def _row_to_todo(self, row) -> Todo:
        """将数据库行转换为Todo对象"""
        return Todo(
            id=row['id'],
            title=row['title'],
            description=row['description'] or "",
            priority=row['priority'],
            category_id=row['category_id'],
            folder_id=row['folder_id'],
            due_date=datetime.fromisoformat(row['due_date']) if row['due_date'] else None,
            completed=bool(row['completed']),
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
            updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
        )
