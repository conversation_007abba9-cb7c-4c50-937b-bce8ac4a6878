"""
数据库管理器
"""
import sqlite3
import os
from typing import List, Optional, Any, Dict
from datetime import datetime
from core.models import Todo, Category, Folder, Setting


class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self, db_path: str = "todolist.db"):
        """初始化数据库管理器"""
        self.db_path = db_path
        self.connection = None
        self.connect()
        self.create_tables()
    
    def connect(self) -> None:
        """建立数据库连接"""
        try:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        except sqlite3.Error as e:
            print(f"数据库连接错误: {e}")
            raise
    
    def create_tables(self) -> None:
        """创建数据表"""
        try:
            # 读取SQL文件
            schema_path = os.path.join("config", "database_schema.sql")
            if os.path.exists(schema_path):
                with open(schema_path, 'r', encoding='utf-8') as f:
                    schema_sql = f.read()
                
                # 执行SQL语句
                cursor = self.connection.cursor()
                cursor.executescript(schema_sql)
                self.connection.commit()
            else:
                print(f"数据库模式文件不存在: {schema_path}")
        except sqlite3.Error as e:
            print(f"创建数据表错误: {e}")
            raise
    
    def execute_query(self, query: str, params: tuple = ()) -> List[sqlite3.Row]:
        """执行查询语句"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            return cursor.fetchall()
        except sqlite3.Error as e:
            print(f"查询执行错误: {e}")
            return []
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """执行更新语句"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            self.connection.commit()
            return cursor.lastrowid
        except sqlite3.Error as e:
            print(f"更新执行错误: {e}")
            return -1
    
    def close(self) -> None:
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
    
    def backup_database(self, backup_path: str) -> bool:
        """备份数据库"""
        try:
            with open(backup_path, 'w') as f:
                for line in self.connection.iterdump():
                    f.write('%s\n' % line)
            return True
        except Exception as e:
            print(f"数据库备份错误: {e}")
            return False

    # 分类管理方法
    def add_category(self, name: str, color: str = '#3498db', icon: str = '💼') -> int:
        """添加分类"""
        query = "INSERT INTO categories (name, color, icon) VALUES (?, ?, ?)"
        return self.execute_update(query, (name, color, icon))

    def update_category(self, category_id: int, name: str = None, color: str = None, icon: str = None) -> bool:
        """更新分类"""
        fields = []
        values = []

        if name is not None:
            fields.append("name = ?")
            values.append(name)
        if color is not None:
            fields.append("color = ?")
            values.append(color)
        if icon is not None:
            fields.append("icon = ?")
            values.append(icon)

        if not fields:
            return False

        values.append(category_id)
        query = f"UPDATE categories SET {', '.join(fields)} WHERE id = ?"
        result = self.execute_update(query, tuple(values))
        return result != -1

    def delete_category(self, category_id: int) -> bool:
        """删除分类"""
        # 先将使用此分类的任务的category_id设为NULL
        self.execute_update("UPDATE todos SET category_id = NULL WHERE category_id = ?", (category_id,))
        # 删除分类
        query = "DELETE FROM categories WHERE id = ?"
        result = self.execute_update(query, (category_id,))
        return result != -1

    def get_categories(self) -> List[Category]:
        """获取所有分类"""
        query = "SELECT * FROM categories ORDER BY name"
        rows = self.execute_query(query)
        return [self._row_to_category(row) for row in rows]

    def get_category_by_id(self, category_id: int) -> Optional[Category]:
        """根据ID获取分类"""
        query = "SELECT * FROM categories WHERE id = ?"
        rows = self.execute_query(query, (category_id,))
        if rows:
            return self._row_to_category(rows[0])
        return None

    # 文件夹管理方法
    def add_folder(self, name: str, parent_id: int = None, color: str = '#95a5a6') -> int:
        """添加文件夹"""
        query = "INSERT INTO folders (name, parent_id, color) VALUES (?, ?, ?)"
        return self.execute_update(query, (name, parent_id, color))

    def update_folder(self, folder_id: int, name: str = None, parent_id: int = None, color: str = None) -> bool:
        """更新文件夹"""
        fields = []
        values = []

        if name is not None:
            fields.append("name = ?")
            values.append(name)
        if parent_id is not None:
            fields.append("parent_id = ?")
            values.append(parent_id)
        if color is not None:
            fields.append("color = ?")
            values.append(color)

        if not fields:
            return False

        values.append(folder_id)
        query = f"UPDATE folders SET {', '.join(fields)} WHERE id = ?"
        result = self.execute_update(query, tuple(values))
        return result != -1

    def delete_folder(self, folder_id: int) -> bool:
        """删除文件夹"""
        # 先将使用此文件夹的任务的folder_id设为NULL
        self.execute_update("UPDATE todos SET folder_id = NULL WHERE folder_id = ?", (folder_id,))
        # 将子文件夹的parent_id设为NULL
        self.execute_update("UPDATE folders SET parent_id = NULL WHERE parent_id = ?", (folder_id,))
        # 删除文件夹
        query = "DELETE FROM folders WHERE id = ?"
        result = self.execute_update(query, (folder_id,))
        return result != -1

    def get_folders(self) -> List[Folder]:
        """获取所有文件夹"""
        query = "SELECT * FROM folders ORDER BY name"
        rows = self.execute_query(query)
        return [self._row_to_folder(row) for row in rows]

    def get_folder_by_id(self, folder_id: int) -> Optional[Folder]:
        """根据ID获取文件夹"""
        query = "SELECT * FROM folders WHERE id = ?"
        rows = self.execute_query(query, (folder_id,))
        if rows:
            return self._row_to_folder(rows[0])
        return None

    def _row_to_category(self, row) -> Category:
        """将数据库行转换为Category对象"""
        return Category(
            id=row['id'],
            name=row['name'],
            color=row['color'] or '#3498db',
            icon=row['icon'] or '💼',
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None
        )

    def _row_to_folder(self, row) -> Folder:
        """将数据库行转换为Folder对象"""
        return Folder(
            id=row['id'],
            name=row['name'],
            parent_id=row['parent_id'],
            color=row['color'] or '#95a5a6',
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None
        )
